import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { safeRoute } from '@/utils/route-helper';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { Edit, Eye, Plus, Search, Trash2, Clock } from 'lucide-react';
import { useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/admin/dashboard',
    },
    {
        title: 'Usuários',
        href: '/admin/usuarios',
    },
];

interface User {
    id: number;
    name: string;
    email: string;
    role: string;
    active: boolean;
    created_at: string;
    phone?: string;
}

interface Props {
    usuarios: {
        data: User[];
        links: any[];
        meta: any;
    };
    filters: {
        role?: string;
        active?: boolean;
        search?: string;
    };
}

export default function AdminUsuarios({ usuarios, filters }: Props) {
    const [search, setSearch] = useState(filters.search || '');
    const { data, setData, get } = useForm({
        role: filters.role || undefined,
        active: filters.active?.toString() || undefined,
        search: filters.search || '',
    });

    const handleFilter = () => {
        try {
            const routeUrl = safeRoute('admin.usuarios.index');
            if (routeUrl !== '#') {
                get(routeUrl, {
                    preserveState: true,
                    replace: true,
                });
            }
        } catch (error) {
            console.error('Erro ao filtrar usuários:', error);
        }
    };

    const handleDelete = (id: number) => {
        router.delete(route('admin.usuarios.destroy', id), {
            onSuccess: () => {
                // Usuário deletado com sucesso
            },
            onError: (errors) => {
                console.error('Erro ao deletar usuário:', errors);
            },
        });
    };

    const handleExport = () => {
        const params = new URLSearchParams();
        if (data.search) params.append('search', data.search);
        if (data.role) params.append('role', data.role);

        const exportUrl = safeRoute('admin.usuarios.export') + (params.toString() ? '?' + params.toString() : '');
        window.open(exportUrl, '_blank');
    };

    const getRoleBadge = (role: string) => {
        const colors = {
            admin: 'bg-red-100 text-red-800',
            fisioterapeuta: 'bg-blue-100 text-blue-800',
            paciente: 'bg-green-100 text-green-800',
            empresa: 'bg-purple-100 text-purple-800',
            afiliado: 'bg-orange-100 text-orange-800',
        };
        return colors[role as keyof typeof colors] || 'bg-gray-100 text-gray-800';
    };

    const getRoleLabel = (role: string) => {
        const labels = {
            admin: 'Administrador',
            fisioterapeuta: 'Fisioterapeuta',
            paciente: 'Paciente',
            empresa: 'Empresa',
            afiliado: 'Afiliado',
        };
        return labels[role as keyof typeof labels] || role;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Usuários" />
            <div className="flex h-full flex-1 flex-col gap-4 overflow-x-auto rounded-xl p-4">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Usuários</h1>
                        <p className="text-gray-600">Gerencie todos os usuários do sistema</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" onClick={handleExport}>
                            <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                />
                            </svg>
                            Exportar
                        </Button>
                        <Link href={safeRoute('admin.usuarios.create')} preserveState>
                            <Button>
                                <Plus className="mr-2 h-4 w-4" />
                                Novo Usuário
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Filtros */}
                <div className="flex items-end gap-4">
                    <div className="flex-1">
                        <Input
                            placeholder="Buscar por nome ou email..."
                            value={search}
                            onChange={(e) => {
                                setSearch(e.target.value);
                                setData('search', e.target.value);
                            }}
                            className="max-w-sm"
                        />
                    </div>
                    <Select value={data.role || undefined} onValueChange={(value) => setData('role', value || undefined)}>
                        <SelectTrigger className="w-48">
                            <SelectValue placeholder="Filtrar por role" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">Todos os roles</SelectItem>
                            <SelectItem value="admin">Administrador</SelectItem>
                            <SelectItem value="fisioterapeuta">Fisioterapeuta</SelectItem>
                            <SelectItem value="paciente">Paciente</SelectItem>
                        </SelectContent>
                    </Select>
                    <Select value={data.active || undefined} onValueChange={(value) => setData('active', value || undefined)}>
                        <SelectTrigger className="w-48">
                            <SelectValue placeholder="Filtrar por status" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">Todos os status</SelectItem>
                            <SelectItem value="true">Ativo</SelectItem>
                            <SelectItem value="false">Inativo</SelectItem>
                        </SelectContent>
                    </Select>
                    <Button onClick={handleFilter}>
                        <Search className="mr-2 h-4 w-4" />
                        Filtrar
                    </Button>
                </div>

                {/* Tabela */}
                <div className="rounded-lg border bg-card">
                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead className="border-b">
                                <tr>
                                    <th className="p-4 text-left font-medium">Nome</th>
                                    <th className="p-4 text-left font-medium">Email</th>
                                    <th className="p-4 text-left font-medium">Role</th>
                                    <th className="p-4 text-left font-medium">Status</th>
                                    <th className="p-4 text-left font-medium">Criado em</th>
                                    <th className="p-4 text-right font-medium">Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                {usuarios.data.map((usuario) => (
                                    <tr key={usuario.id} className="border-b hover:bg-gray-50">
                                        <td className="p-4">
                                            <div>
                                                <p className="font-medium">{usuario.name}</p>
                                                {usuario.phone && <p className="text-sm text-muted-foreground">{usuario.phone}</p>}
                                            </div>
                                        </td>
                                        <td className="p-4 text-muted-foreground">{usuario.email}</td>
                                        <td className="p-4">
                                            <span className={`rounded-full px-2 py-1 text-xs ${getRoleBadge(usuario.role)}`}>
                                                {getRoleLabel(usuario.role)}
                                            </span>
                                        </td>
                                        <td className="p-4">
                                            <span
                                                className={`rounded-full px-2 py-1 text-xs ${
                                                    usuario.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                                }`}
                                            >
                                                {usuario.active ? 'Ativo' : 'Inativo'}
                                            </span>
                                        </td>
                                        <td className="p-4 text-muted-foreground">{new Date(usuario.created_at).toLocaleDateString('pt-BR')}</td>
                                        <td className="p-4">
                                            <div className="flex items-center justify-end gap-2">
                                                <Link href={safeRoute('admin.usuarios.show', usuario.id)} preserveState>
                                                    <Button variant="outline" size="icon" className="h-8 w-8">
                                                        <Eye className="h-4 w-4" />
                                                        <span className="sr-only">Visualizar</span>
                                                    </Button>
                                                </Link>
                                                <Link href={safeRoute('admin.usuarios.edit', usuario.id)} preserveState>
                                                    <Button variant="outline" size="icon" className="h-8 w-8">
                                                        <Edit className="h-4 w-4" />
                                                        <span className="sr-only">Editar</span>
                                                    </Button>
                                                </Link>
                                                <Link href={safeRoute('admin.usuarios.history', usuario.id)} preserveState>
                                                    <Button variant="outline" size="icon" className="h-8 w-8">
                                                        <Clock className="h-4 w-4" />
                                                        <span className="sr-only">Histórico</span>
                                                    </Button>
                                                </Link>
                                                <AlertDialog>
                                                    <AlertDialogTrigger asChild>
                                                        <Button
                                                            variant="outline"
                                                            size="icon"
                                                            className="h-8 w-8 text-red-600 hover:bg-red-50 hover:text-red-700 border-red-200"
                                                        >
                                                            <Trash2 className="h-4 w-4" />
                                                            <span className="sr-only">Excluir</span>
                                                        </Button>
                                                    </AlertDialogTrigger>
                                                    <AlertDialogContent>
                                                        <AlertDialogHeader>
                                                            <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
                                                            <AlertDialogDescription>
                                                                Tem certeza que deseja remover o usuário "{usuario.name}"? Esta ação não pode ser
                                                                desfeita.
                                                            </AlertDialogDescription>
                                                        </AlertDialogHeader>
                                                        <AlertDialogFooter>
                                                            <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                                            <AlertDialogAction
                                                                onClick={() => handleDelete(usuario.id)}
                                                                className="bg-red-600 hover:bg-red-700"
                                                            >
                                                                Excluir
                                                            </AlertDialogAction>
                                                        </AlertDialogFooter>
                                                    </AlertDialogContent>
                                                </AlertDialog>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>

                {/* Paginação */}
                {usuarios.links && usuarios.meta && (
                    <div className="flex items-center justify-between">
                        <p className="text-sm text-muted-foreground">
                            Mostrando {usuarios.meta.from} a {usuarios.meta.to} de {usuarios.meta.total} resultados
                        </p>
                        <div className="flex gap-2">
                            {usuarios.links.map((link: any, index: number) => (
                                <Button
                                    key={index}
                                    variant={link.active ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => link.url && router.get(link.url)}
                                    disabled={!link.url}
                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                />
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
