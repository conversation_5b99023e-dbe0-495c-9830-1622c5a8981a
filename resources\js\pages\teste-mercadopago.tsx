import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import PublicLayout from '@/layouts/public-layout';
import { AlertCircle, Check, CheckCircle, CreditCard, Search, Users, Zap } from 'lucide-react';
import { useState } from 'react';

interface Plano {
    id: string;
    nome: string;
    preco: number;
    periodo: string;
    descricao: string;
    popular: boolean;
    recursos: string[];
    icone: any;
    cor: string;
    ctaText: string;
}

export default function TesteMercadoPago() {
    const [planoSelecionado, setPlanoSelecionado] = useState<Plano | null>(null);
    const [mostrarCheckout, setMostrarCheckout] = useState(false);
    const [dadosPagamento, setDadosPagamento] = useState({
        nome: 'TESTUSER9211',
        email: '<EMAIL>',
        cpf: '12345678909',
    });
    const [statusPagamento, setStatusPagamento] = useState<'pendente' | 'processando' | 'sucesso' | 'erro'>('pendente');

    const planos: Plano[] = [
        {
            id: 'busca',
            nome: 'Plano Busca',
            preco: 14.8,
            periodo: 'mês',
            descricao: 'Encontre fisioterapeutas, farmácias e dentistas próximos a você',
            popular: false,
            recursos: [
                'Busca ilimitada de profissionais',
                'Filtros avançados por especialidade',
                'Visualização de avaliações',
                'Informações de contato',
                'Localização no mapa',
                'Horários de funcionamento',
                'Suporte por chat',
            ],
            icone: Search,
            cor: 'blue',
            ctaText: 'Testar Plano',
        },
        {
            id: 'pessoal',
            nome: 'Plano Pessoal',
            preco: 180.0,
            periodo: 'mês',
            descricao: 'Atendimento fisioterapêutico domiciliar personalizado',
            popular: true,
            recursos: [
                'Todos os recursos do Plano Busca',
                'Agendamento de consultas domiciliares',
                'Fisioterapeutas qualificados',
                'Atendimento personalizado',
                'Relatórios de avaliação',
                'Suporte telefônico',
                'Cancelamento flexível',
                'Histórico médico digital',
            ],
            icone: Users,
            cor: 'green',
            ctaText: 'Testar Plano',
        },
        {
            id: 'empresarial',
            nome: 'Plano Empresarial',
            preco: 640.0,
            periodo: 'mês',
            descricao: 'Solução completa de fisioterapia para sua empresa',
            popular: false,
            recursos: [
                'Todos os recursos do Plano Pessoal',
                'Atendimento para até 20 funcionários',
                'Gestão centralizada',
                'Relatórios empresariais',
                'Programa de prevenção',
                'Treinamentos ergonômicos',
                'Suporte dedicado',
                'Descontos em consultas extras',
                'Dashboard administrativo',
            ],
            icone: Zap,
            cor: 'purple',
            ctaText: 'Testar Plano',
        },
    ];

    const getCorClasses = (cor: string) => {
        const cores = {
            blue: {
                bg: 'bg-blue-50',
                border: 'border-blue-200',
                text: 'text-blue-600',
                button: 'bg-blue-600 hover:bg-blue-700',
                icon: 'text-blue-600',
            },
            green: {
                bg: 'bg-green-50',
                border: 'border-green-200',
                text: 'text-green-600',
                button: 'bg-green-600 hover:bg-green-700',
                icon: 'text-green-600',
            },
            purple: {
                bg: 'bg-purple-50',
                border: 'border-purple-200',
                text: 'text-purple-600',
                button: 'bg-purple-600 hover:bg-purple-700',
                icon: 'text-purple-600',
            },
        };
        return cores[cor as keyof typeof cores] || cores.blue;
    };

    const selecionarPlano = (plano: Plano) => {
        setPlanoSelecionado(plano);
        setMostrarCheckout(true);
        setStatusPagamento('pendente');
    };

    const simularPagamento = async () => {
        // Redirecionar para o checkout do Mercado Pago
        const params = new URLSearchParams({
            plano: planoSelecionado!.nome,
            preco: planoSelecionado!.preco.toString(),
            periodo: planoSelecionado!.periodo,
            descricao: planoSelecionado!.descricao
        });
        
        window.location.href = `/mercadopago/checkout?${params.toString()}`;
    };

    const voltarParaPlanos = () => {
        setPlanoSelecionado(null);
        setMostrarCheckout(false);
        setStatusPagamento('pendente');
    };

    if (mostrarCheckout && planoSelecionado) {
        return (
            <PublicLayout title="Teste de Pagamento - Mercado Pago" description="Página de teste para integração com Mercado Pago">
                <div className="min-h-screen bg-gradient-to-b from-background to-muted/30 py-20">
                    <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
                        {/* Header */}
                        <div className="mb-8 text-center">
                            <h1 className="mb-2 text-3xl font-bold text-primary">Teste de Integração - Mercado Pago</h1>
                            <p className="text-muted-foreground">Simulando o fluxo de pagamento para o {planoSelecionado.nome}</p>
                        </div>

                        {/* Informações do Plano */}
                        <Card className="mb-6">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-3">
                                    <div className={`rounded-full p-2 ${getCorClasses(planoSelecionado.cor).bg}`}>
                                        <planoSelecionado.icone className={`h-5 w-5 ${getCorClasses(planoSelecionado.cor).icon}`} />
                                    </div>
                                    {planoSelecionado.nome}
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground">Preço</Label>
                                        <p className="text-2xl font-bold">
                                            R$ {planoSelecionado.preco.toFixed(2).replace('.', ',')}/{planoSelecionado.periodo}
                                        </p>
                                    </div>
                                    <div>
                                        <Label className="text-sm font-medium text-muted-foreground">Descrição</Label>
                                        <p className="text-sm">{planoSelecionado.descricao}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Dados de Teste */}
                        <Card className="mb-6">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <AlertCircle className="h-5 w-5 text-yellow-600" />
                                    Dados de Teste - Mercado Pago
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                                    <div>
                                        <Label className="text-sm font-medium">País</Label>
                                        <p className="text-sm">🇧🇷 Brasil</p>
                                    </div>
                                    <div>
                                        <Label className="text-sm font-medium">User ID</Label>
                                        <p className="font-mono text-sm">2608760894</p>
                                    </div>
                                    <div>
                                        <Label className="text-sm font-medium">Usuário</Label>
                                        <p className="font-mono text-sm">TESTUSER9211...</p>
                                    </div>
                                </div>
                                <Separator className="my-4" />
                                <div className="rounded-lg bg-yellow-50 p-4">
                                    <p className="text-sm text-yellow-800">
                                        <strong>Importante:</strong> Esta é uma conta de teste do Mercado Pago. Use os dados fornecidos para simular o
                                        pagamento.
                                    </p>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Formulário de Pagamento */}
                        <Card className="mb-6">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <CreditCard className="h-5 w-5" />
                                    Dados do Pagamento
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="nome">Nome Completo</Label>
                                        <Input
                                            id="nome"
                                            value={dadosPagamento.nome}
                                            onChange={(e) => setDadosPagamento((prev) => ({ ...prev, nome: e.target.value }))}
                                            placeholder="Nome completo"
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="email">E-mail</Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            value={dadosPagamento.email}
                                            onChange={(e) => setDadosPagamento((prev) => ({ ...prev, email: e.target.value }))}
                                            placeholder="<EMAIL>"
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="cpf">CPF</Label>
                                        <Input
                                            id="cpf"
                                            value={dadosPagamento.cpf}
                                            onChange={(e) => setDadosPagamento((prev) => ({ ...prev, cpf: e.target.value }))}
                                            placeholder="000.000.000-00"
                                        />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Status do Pagamento */}
                        {statusPagamento !== 'pendente' && (
                            <Card className="mb-6">
                                <CardContent className="pt-6">
                                    <div className="flex items-center gap-3">
                                        {statusPagamento === 'processando' && (
                                            <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-primary"></div>
                                        )}
                                        {statusPagamento === 'sucesso' && <CheckCircle className="h-6 w-6 text-green-600" />}
                                        <span className="font-medium">
                                            {statusPagamento === 'processando' && 'Processando pagamento...'}
                                            {statusPagamento === 'sucesso' && 'Pagamento processado com sucesso!'}
                                        </span>
                                    </div>
                                </CardContent>
                            </Card>
                        )}

                        {/* Botões de Ação */}
                        <div className="flex flex-col justify-center gap-3 sm:flex-row">
                            {statusPagamento === 'pendente' && (
                                <Button onClick={simularPagamento} size="lg" className="bg-green-600 hover:bg-green-700">
                                    Simular Pagamento Mercado Pago
                                </Button>
                            )}

                            {statusPagamento === 'sucesso' && (
                                <Button onClick={() => (window.location.href = '/success')} size="lg" className="bg-primary hover:bg-primary/90">
                                    Ir para Página de Sucesso
                                </Button>
                            )}

                            <Button onClick={voltarParaPlanos} variant="outline" size="lg">
                                Voltar para Planos
                            </Button>
                        </div>

                        {/* Instruções */}
                        <div className="mt-8 text-center">
                            <p className="text-sm text-muted-foreground">
                                Esta página simula o fluxo de pagamento. Em produção, o usuário seria redirecionado para o Mercado Pago para realizar
                                o pagamento real.
                            </p>
                        </div>
                    </div>
                </div>
            </PublicLayout>
        );
    }

    return (
        <PublicLayout title="Teste de Integração - Mercado Pago" description="Página de teste para integração com Mercado Pago">
            <div className="min-h-screen bg-gradient-to-b from-background to-muted/30">
                {/* Hero Section */}
                <section className="relative py-20 md:py-32">
                    <div className="relative z-10 mx-auto w-full max-w-6xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <h1 className="mx-auto max-w-4xl text-4xl font-medium text-balance md:text-5xl">
                                Teste de Integração
                                <span className="block text-primary">Mercado Pago</span>
                            </h1>
                            <p className="mx-auto my-8 max-w-3xl text-xl text-balance text-muted-foreground">
                                Página de teste para validar a integração com o Mercado Pago. Escolha um plano e simule o fluxo de pagamento completo.
                            </p>

                            {/* Banner de Teste */}
                            <div className="mx-auto mb-8 max-w-2xl rounded-lg border border-yellow-200 bg-yellow-50 p-4">
                                <div className="flex items-center gap-2 text-yellow-800">
                                    <AlertCircle className="h-5 w-5" />
                                    <span className="font-medium">Modo de Teste</span>
                                </div>
                                <p className="mt-2 text-sm text-yellow-700">
                                    Esta página utiliza dados de teste do Mercado Pago para simular pagamentos sem realizar transações reais.
                                </p>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Planos */}
                <section className="bg-background py-20">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="mb-12 text-center">
                            <h2 className="text-3xl font-medium text-balance md:text-4xl">Escolha um Plano para Testar</h2>
                            <p className="mx-auto mt-4 max-w-2xl text-xl text-balance text-muted-foreground">
                                Selecione um dos planos abaixo para testar a integração completa
                            </p>
                        </div>

                        <div className="grid gap-8 lg:grid-cols-3">
                            {planos.map((plano) => {
                                const cores = getCorClasses(plano.cor);
                                const IconeComponent = plano.icone;

                                return (
                                    <Card key={plano.id} className={`relative ${plano.popular ? 'scale-105 shadow-lg ring-2 ring-primary' : ''}`}>
                                        {plano.popular && (
                                            <div className="absolute -top-3 left-1/2 -translate-x-1/2">
                                                <Badge className="bg-primary text-primary-foreground">Mais Popular</Badge>
                                            </div>
                                        )}

                                        <CardHeader className="text-center">
                                            <div className={`mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full ${cores.bg}`}>
                                                <IconeComponent className={`h-8 w-8 ${cores.icon}`} />
                                            </div>
                                            <CardTitle className="text-2xl">{plano.nome}</CardTitle>
                                            <p className="text-muted-foreground">{plano.descricao}</p>
                                        </CardHeader>

                                        <CardContent className="space-y-6">
                                            {/* Preço */}
                                            <div className="text-center">
                                                <div className="flex items-baseline justify-center gap-1">
                                                    <span className="text-sm text-muted-foreground">R$</span>
                                                    <span className="text-4xl font-bold">{plano.preco.toFixed(2).replace('.', ',')}</span>
                                                    <span className="text-sm text-muted-foreground">/{plano.periodo}</span>
                                                </div>
                                            </div>

                                            {/* Recursos */}
                                            <div className="space-y-3">
                                                {plano.recursos.map((recurso, index) => (
                                                    <div key={index} className="flex items-start gap-3">
                                                        <Check className="mt-0.5 h-4 w-4 flex-shrink-0 text-green-600" />
                                                        <span className="text-sm">{recurso}</span>
                                                    </div>
                                                ))}
                                            </div>

                                            {/* CTA */}
                                            <div className="pt-4">
                                                <Button
                                                    onClick={() => selecionarPlano(plano)}
                                                    className={`w-full ${cores.button} text-white`}
                                                    size="lg"
                                                >
                                                    {plano.ctaText}
                                                </Button>
                                            </div>
                                        </CardContent>
                                    </Card>
                                );
                            })}
                        </div>
                    </div>
                </section>

                {/* Informações de Teste */}
                <section className="bg-muted/30 py-20">
                    <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
                        <div className="mb-12 text-center">
                            <h2 className="text-3xl font-medium text-balance md:text-4xl">Dados de Teste Disponíveis</h2>
                            <p className="mx-auto mt-4 max-w-2xl text-xl text-balance text-muted-foreground">
                                Utilize as informações abaixo para testar a integração
                            </p>
                        </div>

                        <div className="grid gap-6 md:grid-cols-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <CreditCard className="h-5 w-5" />
                                        Cartões de Teste
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <div className="text-sm">
                                            <strong>Mastercard:</strong> 5031 4332 1540 6351
                                        </div>
                                        <div className="text-sm">
                                            <strong>Visa:</strong> 4235 6477 2802 5682
                                        </div>
                                        <div className="text-sm">
                                            <strong>CVV:</strong> 123 (1234 para Amex)
                                        </div>
                                        <div className="text-sm">
                                            <strong>Vencimento:</strong> 11/30
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Users className="h-5 w-5" />
                                        Conta de Teste
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <div className="text-sm">
                                            <strong>País:</strong> Brasil 🇧🇷
                                        </div>
                                        <div className="text-sm">
                                            <strong>User ID:</strong> 2608760894
                                        </div>
                                        <div className="text-sm">
                                            <strong>Usuário:</strong> TESTUSER9211...
                                        </div>
                                        <div className="text-sm">
                                            <strong>Senha:</strong> C8Wb6MuVJb
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </section>
            </div>
        </PublicLayout>
    );
}
