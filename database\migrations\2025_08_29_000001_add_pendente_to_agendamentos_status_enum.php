<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Adicionar 'pendente' ao enum de status usando raw SQL
        // Isso evita recriar a tabela inteira
        DB::statement("ALTER TABLE agendamentos MODIFY COLUMN status ENUM('agendado', 'confirmado', 'em_andamento', 'concluido', 'cancelado', 'pendente') DEFAULT 'agendado'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove 'pendente' do enum (volta ao estado anterior)
        DB::statement("ALTER TABLE agendamentos MODIFY COLUMN status ENUM('agendado', 'confirmado', 'em_andamento', 'concluido', 'cancelado') DEFAULT 'agendado'");
    }
};
