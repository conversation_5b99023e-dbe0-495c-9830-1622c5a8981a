import '../css/app.css';

import { createInertiaApp } from '@inertiajs/react';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { createRoot } from 'react-dom/client';
import { route } from 'ziggy-js';
import { initializeTheme } from './hooks/use-appearance';

const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

createInertiaApp({
    title: (title) => (title ? `${title} - ${appName}` : appName),
    resolve: (name) => resolvePageComponent(`./pages/${name}.tsx`, import.meta.glob('./pages/**/*.tsx')),
    setup({ el, App, props }) {
        /* eslint-disable */
        // @ts-expect-error
        (window as any).route = (name: any, params: any, absolute: any) =>
            route(name, params as any, absolute, {
                // @ts-expect-error
                ...props.initialPage.props.ziggy,
                // @ts-expect-error
                location: new URL(props.initialPage.props.ziggy.location),
            });
        /* eslint-enable */

        const root = createRoot(el);

        root.render(<App {...props} />);
    },
    progress: {
        color: 'oklch(0.7357 0.2444 143.2345)',
    },
});

// This will set light / dark mode on load...
initializeTheme();
