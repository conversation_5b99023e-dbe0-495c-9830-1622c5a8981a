import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import { useToast } from '@/components/ui/toast-notification';
import { Activity, AlertTriangle, ArrowLeft, CheckCircle, Clock, DollarSign, Package } from 'lucide-react';
import { useEffect, useState } from 'react';

interface Props {
    assinaturaAtual?: {
        id: number;
        status: string;
        data_inicio: string;
        data_fim: string;
        sessoes_restantes: number;
        valor_mensal: number;
        plano: {
            id: number;
            nome: string;
            descricao: string;
            preco: number;
            sessoes_mes: number;
        };
    };
    planosDisponiveis: Array<{
        id: number;
        nome: string;
        descricao: string;
        preco: number;
        sessoes_mes: number;
        beneficios: string[];
    }>;
    historicoAssinaturas: Array<{
        id: number;
        status: string;
        data_inicio: string;
        data_fim: string;
        plano: {
            nome: string;
        };
    }>;
    pagamentosPendentes: Array<{
        id: number;
        valor: number;
        data_vencimento: string;
        status: string;
        forma_pagamento: string;
    }>;
    proximoPagamento?: {
        id: number;
        valor: number;
        data_vencimento: string;
        forma_pagamento: string;
    };
    statsUso: {
        sessoesUsadas: number;
        sessoesRestantes: number;
        percentualUso: number;
        diasRestantes: number;
    };
}

export default function PacientePlano() {
    const pageProps = usePage().props as any;
    const { assinaturaAtual, planosDisponiveis, historicoAssinaturas, pagamentosPendentes, proximoPagamento, statsUso } = pageProps;
    const { showError, showSuccess, showInfo, showWarning } = useToast();
    // Também exibir erros de validação/servidor do Inertia (ex.: errors.plano)
    useEffect(() => {
        const msg = pageProps?.errors?.plano || pageProps?.errors?.message;
        if (msg) {
            showError(String(msg));
        }
    }, [pageProps?.errors, showError]);
    // Garantir valores inteiros para dias restantes no front também (fallback)
    const diasRestantesInt = Math.max(0, Math.floor(Number(statsUso?.diasRestantes ?? 0)));
    // Remover possíveis duplicados no histórico (por id)
    const historicoUnico = Array.isArray(historicoAssinaturas)
        ? Array.from(new Map(historicoAssinaturas.map((h: any) => [h.id, h])).values())
        : [];

    const [showCancelDialog, setShowCancelDialog] = useState(false);
    const [showChangeDialog, setShowChangeDialog] = useState(false);
    const [showConfirmChangeDialog, setShowConfirmChangeDialog] = useState(false);
    const [selectedPlan, setSelectedPlan] = useState<number | null>(null);

    // Preseleciona o outro plano quando o modal abre
    useEffect(() => {
        if (!showChangeDialog) return;
        const currentId = assinaturaAtual?.plano?.id;
        const onlyMainPlans = (planosDisponiveis || []).filter(
            (p: any) => p?.nome === 'Plano Pessoal' || p?.nome === 'Plano Empresarial'
        );
        // Deduplica por nome (normalizado)
        const seen: Record<string, boolean> = {};
        const uniqueMain = onlyMainPlans.filter((p: any) => {
            const key = (p?.nome || '').trim().toLowerCase();
            if (seen[key]) return false;
            seen[key] = true;
            return true;
        });
        const other = uniqueMain.find((p: any) => p.id !== currentId);
        setSelectedPlan(other ? other.id : null);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [showChangeDialog]);

    const { data, setData, post, processing } = useForm({
        plano_id: '',
        forma_pagamento: 'cartao_credito',
        novo_plano_id: '',
    });

    const handleSubscribe = (planoId: number) => {
        setData('plano_id', planoId.toString());
        post(route('paciente.plano.subscribe'));
    };

    const handleCancel = () => {
        post(route('paciente.plano.cancel'), {
            preserveScroll: true,
            onSuccess: () => {
                setShowCancelDialog(false);
            },
            onError: () => {
                // Mesmo em erro, fechamos o modal para mostrar o flash de erro na tela
                setShowCancelDialog(false);
            },
        });
    };

    const handleReactivate = () => {
        post(route('paciente.plano.reactivate'));
    };

    const handleCancelScheduledChange = () => {
        post(route('paciente.plano.change-cancel'), {
            preserveScroll: true,
            onSuccess: () => {
                setShowConfirmChangeDialog(false);
                setShowChangeDialog(false);
            },
            onError: () => {
                setShowConfirmChangeDialog(false);
                setShowChangeDialog(false);
            },
        });
    };

    const handleChange = () => {
        // Open summary dialog instead of posting immediately
        if (!selectedPlan) return;
        setShowConfirmChangeDialog(true);
    };

    const handleConfirmChange = () => {
        if (!selectedPlan) return;
        setData('novo_plano_id', selectedPlan.toString());
        // Fecha em sucesso e também em erro para exibir flashes na tela principal
        post(route('paciente.plano.change'), {
            preserveScroll: true,
            onSuccess: () => {
                // Fecha ambos os modais
                setShowConfirmChangeDialog(false);
                setShowChangeDialog(false);
            },
            onError: (errors: any) => {
                // Em erro (422/500), feche ambos os modais para que os flashes/toasts apareçam na tela principal
                setShowConfirmChangeDialog(false);
                setShowChangeDialog(false);
                // Mostra feedback visual imediato
                const msg = errors?.plano || errors?.message || 'Não foi possível alterar o plano. Tente novamente.';
                showError(String(msg));
            },
            onFinish: () => {
                // Garantir que qualquer estado dependente do "processing" resete corretamente
            },
        });
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('pt-BR');
    };

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(value);
    };

    // Helpers for change summary
    const selectedPlanObj = (planosDisponiveis || []).find((p: any) => p.id === selectedPlan) as
        | { id: number; nome: string; preco: number; sessoes_mes: number }
        | undefined;

    const isUpgrade = selectedPlanObj && assinaturaAtual ? selectedPlanObj.preco > (assinaturaAtual.valor_mensal || 0) : false;

    const calcProportionalDiff = () => {
        if (!assinaturaAtual || !selectedPlanObj) return 0;
        const start = new Date(assinaturaAtual.data_inicio);
        const end = new Date(assinaturaAtual.data_fim);
        const now = new Date();
        const totalMs = end.getTime() - start.getTime();
        const remainingMs = Math.max(0, end.getTime() - now.getTime());
        if (totalMs <= 0) return Math.max(0, selectedPlanObj.preco - (assinaturaAtual.valor_mensal || 0));
        const ratio = remainingMs / totalMs;
        const restanteAtual = (assinaturaAtual.valor_mensal || 0) * ratio;
        const restanteNovo = selectedPlanObj.preco * ratio;
        return Math.max(0, restanteNovo - restanteAtual);
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            ativa: 'default',
            cancelada: 'destructive',
            suspensa: 'secondary',
            expirada: 'outline',
        } as const;

        const labels = {
            ativa: 'Ativa',
            cancelada: 'Cancelada',
            suspensa: 'Suspensa',
            expirada: 'Expirada',
        };

        return <Badge variant={variants[status as keyof typeof variants] || 'default'}>{labels[status as keyof typeof labels] || status}</Badge>;
    };

    const effectiveStatus = assinaturaAtual?.cancel_at_period_end ? 'cancelada' : assinaturaAtual?.status;

    return (
        <AppLayout>
            <Head title="Gerenciar Plano" />

            <div className="flex h-full flex-1 flex-col gap-6 p-4">
                <div>
                    <Link href="/paciente/dashboard">
                        <Button variant="ghost" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Voltar
                        </Button>
                    </Link>
                </div>
                <div>
                    <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl">Gerenciar Plano</h1>
                    <p className="text-muted-foreground">Gerencie sua assinatura e acompanhe o uso do seu plano</p>
                </div>

                {/* Alertas */}
                {pagamentosPendentes.length > 0 && (
                    <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                            Você possui {pagamentosPendentes.length} pagamento(s) pendente(s). Regularize sua situação para continuar usando o
                            serviço.
                        </AlertDescription>
                    </Alert>
                )}

                {assinaturaAtual?.cancel_at_period_end && (
                    <Alert>
                        <Clock className="h-4 w-4" />
                        <AlertDescription>
                            Sua assinatura não será renovada. O cancelamento está agendado para o fim do ciclo e você terá acesso até {assinaturaAtual?.data_fim ? formatDate(assinaturaAtual.data_fim) : 'o fim do mês'}. 
                            Caso mude de ideia, você pode reativar abaixo.
                        </AlertDescription>
                    </Alert>
                )}

                {assinaturaAtual?.scheduled_new_plano && assinaturaAtual?.scheduled_change_date && (
                    <Alert className="border-blue-200 bg-blue-50 text-blue-800">
                        <Clock className="h-4 w-4" />
                        <AlertDescription>
                            Mudança de plano agendada: <strong>{assinaturaAtual.scheduled_new_plano.nome}</strong> a partir de {formatDate(assinaturaAtual.scheduled_change_date)}. 
                            O novo valor passará a valer no próximo ciclo.
                        </AlertDescription>
                    </Alert>
                )}

                {assinaturaAtual ? (
                    <>
                        {/* Plano Atual */}
                        <div className="grid gap-6 md:grid-cols-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Package className="h-5 w-5" />
                                        Plano Atual
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <h3 className="text-lg font-semibold">{assinaturaAtual.plano.nome}</h3>
                                            <p className="text-sm text-muted-foreground">{assinaturaAtual.plano.descricao}</p>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            {getStatusBadge(effectiveStatus)}
                                            {assinaturaAtual?.cancel_at_period_end && (
                                                <Badge variant="secondary">Não renova</Badge>
                                            )}
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <p className="text-muted-foreground">Valor Mensal</p>
                                            <p className="text-lg font-semibold">{formatCurrency(assinaturaAtual.valor_mensal)}</p>
                                        </div>
                                        <div>
                                            <p className="text-muted-foreground">Sessões/Mês</p>
                                            <p className="text-lg font-semibold">{assinaturaAtual.plano.sessoes_mes}</p>
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <div className="flex justify-between text-sm">
                                            <span>Período</span>
                                            <span>
                                                {formatDate(assinaturaAtual.data_inicio)} - {formatDate(assinaturaAtual.data_fim)}
                                            </span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                            <span>Dias Restantes</span>
                                            <span className="font-medium">{diasRestantesInt} dias</span>
                                        </div>
                                    </div>

                                    <div className="flex gap-2">
                                        {assinaturaAtual?.scheduled_new_plano && assinaturaAtual?.scheduled_change_date ? (
                                            <Button variant="outline" size="sm" onClick={handleCancelScheduledChange} disabled={processing}>
                                                Cancelar mudança de plano
                                            </Button>
                                        ) : (
                                            <Button variant="outline" size="sm" onClick={() => setShowChangeDialog(true)}>
                                                Alterar Plano
                                            </Button>
                                        )}
                                        {assinaturaAtual?.cancel_at_period_end ? (
                                            <Button size="sm" onClick={handleReactivate} disabled={processing}>
                                                Reativar
                                            </Button>
                                        ) : (
                                            <Button variant="destructive" size="sm" onClick={() => setShowCancelDialog(true)}>
                                                Cancelar
                                            </Button>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Uso do Plano */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Activity className="h-5 w-5" />
                                        Uso do Plano
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <div className="flex justify-between text-sm">
                                            <span>Sessões Utilizadas</span>
                                            <span>
                                                {statsUso.sessoesUsadas} de {assinaturaAtual.plano.sessoes_mes}
                                            </span>
                                        </div>
                                        <Progress value={statsUso.percentualUso} className="h-2" />
                                        <p className="text-center text-xs text-muted-foreground">{statsUso.percentualUso}% utilizado</p>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="rounded-lg bg-muted p-3 text-center">
                                            <p className="text-2xl font-bold text-green-600">{statsUso.sessoesRestantes}</p>
                                            <p className="text-xs text-muted-foreground">Sessões Restantes</p>
                                        </div>
                                        <div className="rounded-lg bg-muted p-3 text-center">
                                            <p className="text-2xl font-bold text-blue-600">{diasRestantesInt}</p>
                                            <p className="text-xs text-muted-foreground">Dias Restantes</p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Próximo Pagamento */}
                        {proximoPagamento && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <DollarSign className="h-5 w-5" />
                                        Próximo Pagamento
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-lg font-semibold">{formatCurrency(proximoPagamento.valor)}</p>
                                            <p className="text-sm text-muted-foreground">
                                                Vencimento: {formatDate(proximoPagamento.data_vencimento)}
                                            </p>
                                            <p className="text-sm text-muted-foreground">Forma de pagamento: {proximoPagamento.forma_pagamento}</p>
                                        </div>
                                        <Button>Pagar Agora</Button>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </>
                ) : (
                    /* Escolher Plano */
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Escolha seu Plano</CardTitle>
                                <p className="text-muted-foreground">Selecione o plano que melhor atende às suas necessidades</p>
                            </CardHeader>
                        </Card>

                        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                            {planosDisponiveis.map((plano: any) => (
                                <Card key={plano.id} className="relative">
                                    <CardHeader>
                                        <CardTitle>{plano.nome}</CardTitle>
                                        <p className="text-sm text-muted-foreground">{plano.descricao}</p>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="text-center">
                                            <p className="text-3xl font-bold">{formatCurrency(plano.preco)}</p>
                                            <p className="text-sm text-muted-foreground">por mês</p>
                                        </div>

                                        <div className="text-center">
                                            <p className="text-lg font-semibold">{plano.sessoes_mes} sessões</p>
                                            <p className="text-sm text-muted-foreground">por mês</p>
                                        </div>

                                        {plano.beneficios && plano.beneficios.length > 0 && (
                                            <div className="space-y-2">
                                                <p className="text-sm font-medium">Benefícios:</p>
                                                <ul className="space-y-1 text-sm text-muted-foreground">
                                                    {plano.beneficios.map((beneficio: string, index: number) => (
                                                        <li key={index} className="flex items-center gap-2">
                                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                                            {beneficio}
                                                        </li>
                                                    ))}
                                                </ul>
                                            </div>
                                        )}

                                        <Button className="w-full" onClick={() => handleSubscribe(plano.id)} disabled={processing}>
                                            {processing ? 'Processando...' : 'Escolher Plano'}
                                        </Button>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                )}

                {/* Histórico de Assinaturas */}
                {historicoUnico.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Clock className="h-5 w-5" />
                                Histórico de Assinaturas
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {historicoUnico.map((assinatura: any) => (
                                    <div key={assinatura.id} className="flex items-center justify-between rounded-lg border p-3">
                                        <div>
                                            <p className="font-medium">{assinatura.plano.nome}</p>
                                            <p className="text-sm text-muted-foreground">
                                                {formatDate(assinatura.data_inicio)} - {formatDate(assinatura.data_fim)}
                                            </p>
                                        </div>
                                        {getStatusBadge(assinatura.status)}
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}
                {/* Dialog: Alterar Plano */}
                <Dialog open={showChangeDialog} onOpenChange={setShowChangeDialog}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Alterar Plano</DialogTitle>
                            <DialogDescription>Selecione um dos planos disponíveis abaixo para trocar imediatamente.</DialogDescription>
                        </DialogHeader>
                        {(() => {
                            const onlyMainPlans = (planosDisponiveis || []).filter(
                                (p: any) => p?.nome === 'Plano Pessoal' || p?.nome === 'Plano Empresarial'
                            );
                            // Deduplica por nome (client-side)
                            const seen: Record<string, boolean> = {};
                            const uniqueMain = onlyMainPlans.filter((p: any) => {
                                const key = (p?.nome || '').trim().toLowerCase();
                                if (seen[key]) return false;
                                seen[key] = true;
                                return true;
                            });
                            const currentId = assinaturaAtual?.plano?.id;

                            return (
                                <div className="space-y-3">
                                    {uniqueMain.map((plano: any) => {
                                        const isCurrent = plano.id === currentId;
                                        const isSelected = selectedPlan === plano.id;
                                        return (
                                            <div
                                                key={plano.id}
                                                className={`flex items-center justify-between rounded-md border p-3 ${
                                                    isSelected || isCurrent ? 'border-primary' : ''
                                                }`}
                                            >
                                                <div>
                                                    <p className="font-medium">{plano.nome}</p>
                                                    <p className="text-xs text-muted-foreground">{plano.sessoes_mes} sessões/mês • R$ {plano.preco}</p>
                                                </div>
                                                <Button
                                                    variant={isSelected ? 'default' : 'outline'}
                                                    size="sm"
                                                    onClick={() => !isCurrent && setSelectedPlan(plano.id)}
                                                    disabled={isCurrent}
                                                >
                                                    {isCurrent ? 'Atual' : isSelected ? 'Selecionado' : 'Selecionar'}
                                                </Button>
                                            </div>
                                        );
                                    })}
                                    {uniqueMain.length === 0 && (
                                        <p className="text-sm text-muted-foreground">Nenhum outro plano disponível no momento.</p>
                                    )}
                                </div>
                            );
                        })()}
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setShowChangeDialog(false)}>Cancelar</Button>
                            <Button onClick={handleChange} disabled={!selectedPlan || processing}>Confirmar troca</Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                {/* Dialog: Resumo da Troca */}
                <Dialog open={showConfirmChangeDialog} onOpenChange={setShowConfirmChangeDialog}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Confirmar Troca de Plano</DialogTitle>
                            <DialogDescription>
                                {isUpgrade ? (
                                    <span>
                                        Você está fazendo um upgrade. A cobrança adicional proporcional será realizada agora e você será redirecionado ao
                                        Mercado Pago para concluir o pagamento.
                                    </span>
                                ) : (
                                    <span>
                                        Você está fazendo um downgrade. A alteração será aplicada em {assinaturaAtual?.data_fim ? formatDate(assinaturaAtual.data_fim) : 'breve'},
                                        e o novo valor passará a valer no próximo ciclo.
                                    </span>
                                )}
                            </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-3 text-sm">
                            <div className="grid grid-cols-2 gap-3">
                                <div className="rounded-md border p-3">
                                    <p className="text-muted-foreground">Plano Atual</p>
                                    <p className="font-medium">{assinaturaAtual?.plano?.nome}</p>
                                    <p className="text-muted-foreground">{formatCurrency(assinaturaAtual?.valor_mensal || 0)} • {assinaturaAtual?.plano?.sessoes_mes} sessões/mês</p>
                                </div>
                                <div className="rounded-md border p-3">
                                    <p className="text-muted-foreground">Novo Plano</p>
                                    <p className="font-medium">{selectedPlanObj?.nome}</p>
                                    <p className="text-muted-foreground">{formatCurrency(selectedPlanObj?.preco || 0)} • {selectedPlanObj?.sessoes_mes} sessões/mês</p>
                                </div>
                            </div>
                            {isUpgrade && (
                                <div className="rounded-md bg-muted p-3">
                                    <p className="text-muted-foreground">Estimativa de cobrança imediata (proporcional)</p>
                                    <p className="text-lg font-semibold">{formatCurrency(calcProportionalDiff())}</p>
                                </div>
                            )}
                        </div>
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setShowConfirmChangeDialog(false)}>Voltar</Button>
                            <Button onClick={handleConfirmChange} disabled={processing}>
                                {processing ? 'Processando...' : 'Confirmar'}
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                {/* Dialog: Cancelar Plano */}
                <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Cancelar Assinatura</DialogTitle>
                            <DialogDescription>
                                O cancelamento será agendado para o final do ciclo atual. Você continuará com acesso até {assinaturaAtual?.data_fim ? formatDate(assinaturaAtual.data_fim) : 'o fim do mês'}.
                            </DialogDescription>
                        </DialogHeader>
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setShowCancelDialog(false)}>Voltar</Button>
                            <Button variant="destructive" onClick={handleCancel} disabled={processing}>
                                {processing ? 'Processando...' : 'Confirmar cancelamento'}
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
}
