<?php

namespace App\Http\Controllers;

use App\Models\Agendamento;
use App\Models\Disponibilidade;
use App\Models\Fisioterapeuta;
use App\Models\User;
use App\Services\HorarioDisponibilidadeService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AgendamentoController extends Controller
{
    protected $horarioService;

    public function __construct(HorarioDisponibilidadeService $horarioService)
    {
        $this->horarioService = $horarioService;
    }

    /**
     * Buscar horários disponíveis para um fisioterapeuta em uma data específica
     */
    public function horariosDisponiveis(Request $request)
    {
        $validated = $request->validate([
            'fisioterapeuta_id' => 'required|exists:users,id',
            'data' => 'required|date|after_or_equal:today',
        ]);

        $fisioterapeutaId = $validated['fisioterapeuta_id'];
        $data = Carbon::parse($validated['data']);

        $horarios = $this->horarioService->calcularHorariosDisponiveis($fisioterapeutaId, $data);

        return response()->json([
            'success' => true,
            'horarios' => $horarios,
            'data' => $data->format('d/m/Y'),
            'dia_semana' => $data->locale('pt_BR')->dayName,
        ]);
    }

    /**
     * Buscar fisioterapeutas disponíveis para uma data e horário específicos
     */
    public function fisioterapeutasDisponiveis(Request $request)
    {
        $validated = $request->validate([
            'data' => 'required|date|after:today',
            'horario' => 'required|date_format:H:i',
            'area' => 'nullable|string',
            'especializacao' => 'nullable|string',
        ]);

        $data = Carbon::parse($validated['data']);
        $horario = $validated['horario'];

        $query = User::where('active', true)
            ->where('banned', false)
            ->where('suspended', false)
            ->whereHas('fisioterapeuta', function ($q) {
                $q->where('status', 'approved')
                  ->where('available', true);
            })
            ->with(['fisioterapeuta']);

        // Filtrar por área se especificada
        if (!empty($validated['area'])) {
            $query->whereHas('fisioterapeuta', function ($q) use ($validated) {
                $q->whereJsonContains('available_areas', $validated['area']);
            });
        }

        // Filtrar por especialização se especificada
        if (!empty($validated['especializacao'])) {
            $query->whereHas('fisioterapeuta', function ($q) use ($validated) {
                $q->whereJsonContains('specializations', $validated['especializacao']);
            });
        }

        $fisioterapeutas = $query->get();

        // Filtrar apenas os que têm disponibilidade no horário solicitado
        $fisioterapeutasDisponiveis = $fisioterapeutas->filter(function ($fisioterapeuta) use ($data, $horario) {
            return $this->verificarDisponibilidade($fisioterapeuta->id, $data, $horario);
        });

        return response()->json([
            'success' => true,
            'fisioterapeutas' => $fisioterapeutasDisponiveis->values(),
            'total' => $fisioterapeutasDisponiveis->count(),
        ]);
    }

    /**
     * Verificar se um horário específico está disponível (API)
     */
    public function verificarDisponibilidadeApi(Request $request)
    {
        $validated = $request->validate([
            'fisioterapeuta_id' => 'required|exists:users,id',
            'data_hora' => 'required|date|after:now',
        ]);

        $fisioterapeutaId = $validated['fisioterapeuta_id'];
        $dataHora = Carbon::parse($validated['data_hora']);

        $disponivel = $this->verificarDisponibilidade($fisioterapeutaId, $dataHora->toDateString(), $dataHora->format('H:i'));

        return response()->json([
            'success' => true,
            'disponivel' => $disponivel,
            'data_hora' => $dataHora->format('d/m/Y H:i'),
        ]);
    }

    /**
     * Calcular horários disponíveis para um fisioterapeuta em uma data
     */
    private function calcularHorariosDisponiveis($fisioterapeutaId, Carbon $data)
    {
        $horarios = [];
        $diaSemana = $data->dayOfWeek;

        // Buscar disponibilidades do fisioterapeuta para esta data
        $disponibilidades = Disponibilidade::porFisioterapeuta($fisioterapeutaId)
            ->ativas()
            ->disponiveis()
            ->porPeriodo($data, $data)
            ->get();

        // Buscar bloqueios para esta data
        $bloqueios = Disponibilidade::porFisioterapeuta($fisioterapeutaId)
            ->ativas()
            ->bloqueios()
            ->porPeriodo($data, $data)
            ->get();

        // Buscar agendamentos existentes para esta data
        $agendamentosExistentes = Agendamento::where('fisioterapeuta_id', $fisioterapeutaId)
            ->whereDate('scheduled_at', $data)
            ->whereNotIn('status', ['cancelado'])
            ->pluck('scheduled_at')
            ->map(fn($dt) => Carbon::parse($dt)->format('H:i'))
            ->toArray();

        foreach ($disponibilidades as $disponibilidade) {
            // Verificar se esta disponibilidade se aplica ao dia da semana
            if ($disponibilidade->dias_semana && !in_array($diaSemana, $disponibilidade->dias_semana)) {
                continue;
            }

            $horaInicio = Carbon::parse($disponibilidade->hora_inicio);
            $horaFim = Carbon::parse($disponibilidade->hora_fim);

            // Gerar slots de 1 hora
            $horaAtual = $horaInicio->copy();
            while ($horaAtual->lt($horaFim)) {
                $horarioString = $horaAtual->format('H:i');

                // Verificar se não há agendamento neste horário
                if (in_array($horarioString, $agendamentosExistentes)) {
                    $horaAtual->addHour();
                    continue;
                }

                // Verificar se não há bloqueio neste horário
                $bloqueado = $bloqueios->some(function ($bloqueio) use ($data, $horarioString) {
                    return $bloqueio->isBloqueado($data->toDateString(), $horarioString);
                });

                if (!$bloqueado) {
                    $horarios[] = [
                        'horario' => $horarioString,
                        'disponivel' => true,
                        'data_hora' => $data->copy()->setTimeFromTimeString($horarioString)->toISOString(),
                    ];
                }

                $horaAtual->addHour();
            }
        }

        return collect($horarios)->sortBy('horario')->values()->toArray();
    }

    /**
     * Verificar disponibilidade de um fisioterapeuta em data/hora específica
     */
    private function verificarDisponibilidade($fisioterapeutaId, $data, $horario)
    {
        return $this->horarioService->verificarDisponibilidade($fisioterapeutaId, $data, $horario);
    }

    /**
     * Buscar próximos horários disponíveis para um fisioterapeuta
     */
    public function proximosHorariosDisponiveis(Request $request)
    {
        $validated = $request->validate([
            'fisioterapeuta_id' => 'required|exists:users,id',
            'dias' => 'integer|min:1|max:30',
        ]);

        $fisioterapeutaId = $validated['fisioterapeuta_id'];
        $dias = $validated['dias'] ?? 7;

        $horarios = $this->horarioService->obterProximosHorarios($fisioterapeutaId, $dias);

        return response()->json([
            'success' => true,
            'horarios' => $horarios,
            'periodo' => [
                'inicio' => $dataInicio->format('d/m/Y'),
                'fim' => $dataFim->format('d/m/Y'),
            ],
        ]);
    }
}
