<?php

namespace App\Http\Controllers\Paciente;

use App\Http\Controllers\Controller;
use App\Models\Plano;
use App\Models\Assinatura;
use App\Services\AffiliateTrackingService;
use App\Services\MercadoPagoService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class PlanosController extends Controller
{
    /**
     * Exibe a página de seleção de planos
     */
    public function index()
    {
        $user = Auth::user();
        
        // Se o usuário já tem uma assinatura ativa, redireciona para a página de gerenciamento
        if ($user->has_subscription) {
            $assinaturaAtiva = Assinatura::where('user_id', $user->id)
                ->whereIn('status', ['ativa', 'trialing'])
                ->where('end_date', '>', now())
                ->first();
                
            if ($assinaturaAtiva) {
                return redirect()->route('paciente.plano.index');
            }
        }
        
        $planos = [];
        
        // Se não for paciente, incluir o plano de busca
        if (!$user || $user->role !== 'paciente') {
            $planos[] = [
                'id' => 'plano-busca',
                'name' => 'Plano Busca',
                'price' => 14.80,
                'description' => 'Encontre fisioterapeutas, farmácias e dentistas próximos a você',
                'features' => [
                    'Busca ilimitada de profissionais',
                    'Filtros avançados por especialidade',
                    'Visualização de avaliações',
                    'Informações de contato',
                    'Localização no mapa',
                    'Horários de funcionamento',
                    'Suporte por chat',
                ],
                'type' => 'busca'
            ];
        }
        
        // Adicionar planos padrão diretamente
        $planos[] = [
            'id' => 'plano-pessoal',
            'name' => 'Plano Pessoal',
            'price' => 180.00,
            'description' => 'Atendimento fisioterapêutico domiciliar personalizado',
            'features' => [
                'Todos os recursos do Plano Busca',
                'Agendamento de consultas domiciliares',
                'Fisioterapeutas qualificados',
                'Atendimento personalizado',
                'Relatórios de avaliação',
                'Suporte telefônico',
                'Cancelamento flexível',
                'Histórico médico digital',
            ],
            'type' => 'pessoal',
            'popular' => true
        ];
        
        $planos[] = [
            'id' => 'plano-empresarial',
            'name' => 'Plano Empresarial',
            'price' => 640.00,
            'description' => 'Solução completa de fisioterapia para sua empresa',
            'features' => [
                'Todos os recursos do Plano Pessoal',
                'Atendimento para até 20 funcionários',
                'Gestão centralizada',
                'Relatórios empresariais',
                'Programa de prevenção',
                'Treinamentos ergonômicos',
                'Descontos em consultas extras',
                'Dashboard administrativo',
            ],
            'type' => 'empresarial'
        ];

        // Verificar se o usuário já tem um plano selecionado
        $planoAtual = null;
        if ($user->has_subscription) {
            $assinatura = \App\Models\Assinatura::where('user_id', $user->id)
                ->whereIn('status', ['ativa', 'trialing'])
                ->where('end_date', '>', now())
                ->first();

            if ($assinatura) {
                // Determinar o tipo de plano baseado no preço
                $planoType = 'pessoal'; // default
                if ($assinatura->monthly_price == 14.80) {
                    $planoType = 'busca';
                    $planoName = 'Plano Busca';
                } elseif ($assinatura->monthly_price == 180.00) {
                    $planoType = 'pessoal';
                    $planoName = 'Plano Pessoal';
                } elseif ($assinatura->monthly_price == 640.00) {
                    $planoType = 'empresarial';
                    $planoName = 'Plano Empresarial';
                }

                $planoAtual = [
                    'type' => $planoType,
                    'name' => $planoName,
                    'status' => $assinatura->status
                ];
            }
        }

        return Inertia::render('paciente/planos', [
            'planos' => $planos,
            'planoAtual' => $planoAtual,
            'hasSubscription' => $user->has_subscription
        ]);
    }

    /**
     * Processa a seleção do plano
     */
    public function store(Request $request)
    {
        $request->validate([
            'plano_type' => 'required|in:busca,pessoal,empresarial',
        ]);

        $user = Auth::user();
        
        // Verificar se o usuário é paciente e está tentando acessar o plano de busca
        if ($user && $user->role === 'paciente' && $request->plano_type === 'busca') {
            return back()->with('error', 'Plano não disponível para pacientes.');
        }
        
        // Verificar se o usuário já tem um plano ativo
        $assinaturaAtiva = Assinatura::where('user_id', $user->id)
            ->whereIn('status', ['ativa', 'trialing'])
            ->where('end_date', '>', now())
            ->first();

        // Se já tem um plano ativo e está tentando ativar o mesmo plano, redireciona
        if ($assinaturaAtiva && $assinaturaAtiva->plano->type === $request->plano_type) {
            return redirect()->route('paciente.dashboard')
                ->with('info', 'Você já possui este plano ativo.');
        }

        // Processa com base no tipo de plano
        switch ($request->plano_type) {
            case 'busca':
                return $this->processBuscaPlan($user);
            case 'pessoal':
                return $this->processPessoalPlan($user, $request);
            case 'empresarial':
                return $this->processEmpresarialPlan($user, $request);
        }

        return back()->with('error', 'Tipo de plano inválido.');
    }



    /**
     * Processa plano busca (gratuito)
     */
    private function processBuscaPlan($user)
    {
        // Se já tem assinatura, cancelar primeiro
        if ($user->has_subscription) {
            $assinaturaAtiva = \App\Models\Assinatura::where('user_id', $user->id)
                ->where('status', 'ativa')
                ->first();

            if ($assinaturaAtiva) {
                $assinaturaAtiva->update(['status' => 'cancelada']);
                Log::info('Cancelled previous subscription for plan change', [
                    'user_id' => $user->id,
                    'old_subscription_id' => $assinaturaAtiva->id
                ]);
            }
        }

        // Buscar ou criar um plano busca
        $plano = \App\Models\Plano::firstOrCreate(
            ['name' => 'Plano Busca'],
            [
                'name' => 'Plano Busca',
                'description' => 'Busca ilimitada de profissionais de fisioterapia',
                'price' => 14.80,
                'sessions_per_month' => 0,
                'active' => true,
            ]
        );

        // Criar assinatura gratuita para plano busca
        $assinatura = \App\Models\Assinatura::create([
            'user_id' => $user->id,
            'plano_id' => $plano->id,
            'status' => 'ativa',
            'start_date' => \Carbon\Carbon::now(),
            'end_date' => \Carbon\Carbon::now()->addMonth(),
            'sessions_used' => 0,
            'current_period_start' => \Carbon\Carbon::now(),
            'current_period_end' => \Carbon\Carbon::now()->addMonth(),
            'monthly_price' => 14.80,
        ]);

        // Marcar como tendo assinatura ativa
        $user->update([
            'plan_selected' => true,
            'plan_selected_at' => now(),
            'checkout_completed' => true,
            'checkout_completed_at' => now(),
            'has_subscription' => true,
        ]);

        Log::info('Plano Busca ativado', [
            'user_id' => $user->id,
            'assinatura_id' => $assinatura->id
        ]);

        return redirect()->route('paciente.dashboard')
            ->with('success', 'Plano Busca ativado com sucesso!');
    }

    /**
     * Processa plano pessoal (pago)
     */
    private function processPessoalPlan($user, $request)
    {
        $monthlyPrice = 180.00;

        // Cancelar assinatura anterior se existir
        if ($user->has_subscription) {
            $assinaturaAtiva = \App\Models\Assinatura::where('user_id', $user->id)
                ->where('status', 'ativa')
                ->first();

            if ($assinaturaAtiva) {
                $assinaturaAtiva->update(['status' => 'cancelada']);
                Log::info('Cancelled previous subscription for plan change', [
                    'user_id' => $user->id,
                    'old_subscription_id' => $assinaturaAtiva->id
                ]);
            }
        }

        // Buscar ou criar um plano pessoal
        $plano = \App\Models\Plano::firstOrCreate(
            ['name' => 'Plano Pessoal'],
            [
                'name' => 'Plano Pessoal',
                'description' => 'Atendimento fisioterapêutico domiciliar personalizado',
                'price' => $monthlyPrice,
                'sessions_per_month' => 1,
                'active' => true,
            ]
        );

        // Criar assinatura
        $assinatura = Assinatura::create([
            'user_id' => $user->id,
            'plano_id' => $plano->id,
            'status' => 'suspensa', // Suspensa até confirmação do pagamento
            'start_date' => Carbon::now(),
            'end_date' => Carbon::now()->addMonth(),
            'sessions_used' => 0,
            'current_period_start' => Carbon::now(),
            'current_period_end' => Carbon::now()->addMonth(),
            'monthly_price' => $monthlyPrice,
        ]);

        // Criar primeiro pagamento
        $pagamento = \App\Models\Pagamento::create([
            'assinatura_id' => $assinatura->id,
            'amount' => $monthlyPrice,
            'status' => 'pendente',
            'due_date' => Carbon::now()->addDays(7),
            'method' => 'cartao_credito',
            'notes' => "Pagamento mensal - Plano Pessoal",
            'gateway_response' => [
                'plano_type' => 'pessoal',
                'first_payment' => true,
            ]
        ]);

        // Processar venda de afiliado se houver referência
        $affiliateService = new AffiliateTrackingService();
        $affiliateService->createAffiliateSale(
            $user,
            $assinatura->id,
            'pessoal',
            $monthlyPrice,
            $request
        );

        // Marcar plano como selecionado
        $user->update([
            'plan_selected' => true,
            'plan_selected_at' => now(),
        ]);

        // Redirecionar para página de pagamento do paciente, onde ele escolhe o método
        return redirect()->route('paciente.pagamentos.show', $pagamento);
    }

    /**
     * Processa plano empresarial
     */
    private function processEmpresarialPlan($user, $request)
    {
        // Redirecionar para WhatsApp para planos empresariais
        $message = urlencode(
            'Olá! Gostaria de saber mais sobre o plano empresarial de fisioterapia domiciliar para grupos/empresas. Meu nome é ' . $user->name . ' e meu email é ' . $user->email . '.'
        );

        return redirect()->away("https://wa.me/5511978196207?text={$message}");
    }



    /**
     * Criar assinatura recorrente no Mercado Pago
     */
    private function createMercadoPagoPreference($user, $assinatura, $pagamento)
    {
        try {
            // Usar injeção de dependência em vez de instância direta
            $mercadoPagoService = app(MercadoPagoService::class);

            $subscriptionData = [
                'title' => 'Assinatura Plano Pessoal - F4 Fisio',
                'amount' => $pagamento->amount,
                'payer' => [
                    'name' => $user->name,
                    'email' => $user->email,
                ],
                'external_reference' => $pagamento->id,
                'success_url' => route('paciente.pagamentos.success'),
                'notification_url' => route('mercadopago.webhook'),
            ];

            $subscription = $mercadoPagoService->createSubscription($subscriptionData);

            if (!$subscription['success']) {
                Log::error('Erro ao criar assinatura Mercado Pago', [
                    'user_id' => $user->id,
                    'pagamento_id' => $pagamento->id,
                    'error' => $subscription['message'] ?? 'Erro desconhecido'
                ]);

                return back()->with('error', 'Erro ao processar assinatura. Tente novamente.');
            }

            // Atualizar pagamento com dados da assinatura
            $pagamento->update([
                'transaction_id' => $subscription['subscription_id'],
                'gateway_response' => $subscription,
            ]);

            // Atualizar assinatura com ID do Mercado Pago
            $assinatura->update([
                'mercadopago_subscription_id' => $subscription['subscription_id'],
            ]);

            $checkoutUrl = $subscription['init_point'];

            Log::info('Mercado Pago subscription created', [
                'user_id' => $user->id,
                'pagamento_id' => $pagamento->id,
                'subscription_id' => $subscription['subscription_id'],
                'checkout_url' => $checkoutUrl,
            ]);

            // Redirecionar diretamente para o Mercado Pago
            return redirect($checkoutUrl);

        } catch (\Exception $e) {
            Log::error('Erro ao criar preferência Mercado Pago', [
                'user_id' => $user->id,
                'pagamento_id' => $pagamento->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return back()->with('error', 'Erro interno. Tente novamente ou entre em contato com o suporte.');
        }
    }

    /**
     * Retorna dados do plano baseado no tipo
     */
    private function getPlanoData($type)
    {
        $planos = [
            'busca' => ['price' => 14.80, 'sessions' => 0],
            'pessoal' => ['price' => 180.00, 'sessions' => 1],
            'empresarial' => ['price' => 640.00, 'sessions' => 4],
        ];

        return $planos[$type] ?? ['price' => 0, 'sessions' => 0];
    }

    /**
     * Verificar se o Mercado Pago está configurado
     */
    public function checkMercadoPagoStatus()
    {
        $mercadoPagoService = new MercadoPagoService();

        // Verificar se está em modo simulação ou se tem credenciais configuradas
        $isConfigured = config('services.mercadopago.access_token') ||
                       config('services.mercadopago.simulate', false) ||
                       config('app.env') === 'local';

        return response()->json([
            'configured' => $isConfigured,
            'simulation_mode' => config('app.env') === 'local'
        ]);
    }
}
