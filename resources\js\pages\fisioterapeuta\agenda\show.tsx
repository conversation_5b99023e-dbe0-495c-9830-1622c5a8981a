import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { formatCurrency, formatDateTime } from '@/lib/utils';
import { Head, Link, router } from '@inertiajs/react';
import {
    AlertTriangle,
    ArrowLeft,
    Calendar,
    CheckCircle,
    Clock,
    DollarSign,
    FileText,
    Mail,
    MapPin,
    Phone,
    Play,
    Square,
    User,
    XCircle,
} from 'lucide-react';

interface Agendamento {
    id: number;
    data_hora: string;
    status: 'agendado' | 'confirmado' | 'em_andamento' | 'concluido' | 'cancelado';
    endereco: string;
    observacoes?: string;
    valor: number;
    paciente: {
        id: number;
        name: string;
        email: string;
        phone?: string;
    };
    formatted_data_hora: string;
    formatted_valor: string;
    tempo_restante?: string;
    pode_iniciar: boolean;
    started_at?: string;
    finished_at?: string;
    cancelled_at?: string;
    created_at: string;
    relatorio_sessao?: {
        id: number;
        observacoes: string;
        exercicios_realizados: string[];
        proximos_passos: string;
        created_at: string;
    };
}

interface Props {
    agendamento: Agendamento;
}

export default function AgendaShow({ agendamento }: Props) {
    const handleConfirmar = () => {
        router.post(
            route('fisioterapeuta.agenda.confirmar', agendamento.id),
            {},
            {
                preserveScroll: true,
            },
        );
    };

    const handleIniciar = () => {
        router.post(
            route('fisioterapeuta.agenda.iniciar', agendamento.id),
            {},
            {
                preserveScroll: true,
            },
        );
    };

    const handleFinalizar = () => {
        router.post(
            route('fisioterapeuta.agenda.finalizar', agendamento.id),
            {},
            {
                preserveScroll: true,
            },
        );
    };

    const handleCancelar = () => {
        if (confirm('Tem certeza que deseja cancelar este agendamento?')) {
            router.post(
                route('fisioterapeuta.agenda.cancelar', agendamento.id),
                {},
                {
                    preserveScroll: true,
                },
            );
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'agendado':
                return 'bg-blue-100 text-blue-800';
            case 'confirmado':
                return 'bg-green-100 text-green-800';
            case 'em_andamento':
                return 'bg-yellow-100 text-yellow-800';
            case 'concluido':
                return 'bg-gray-100 text-gray-800';
            case 'cancelado':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusLabel = (status: string) => {
        switch (status) {
            case 'agendado':
                return 'Agendado';
            case 'confirmado':
                return 'Confirmado';
            case 'em_andamento':
                return 'Em Andamento';
            case 'concluido':
                return 'Concluído';
            case 'cancelado':
                return 'Cancelado';
            default:
                return status;
        }
    };

    return (
        <AppLayout>
            <Head title={`Agendamento #${agendamento.id}`} />

            <div className="py-12">
                <div className="mx-auto max-w-4xl sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                <Link href={route('fisioterapeuta.agenda.index')}>
                                    <Button variant="ghost" size="sm">
                                        <ArrowLeft className="mr-2 h-4 w-4" />
                                        Voltar
                                    </Button>
                                </Link>
                                <div>
                                    <h2 className="text-3xl font-bold tracking-tight">Agendamento #{agendamento.id}</h2>
                                    <p className="text-muted-foreground">Detalhes do agendamento com {agendamento.paciente.name}</p>
                                </div>
                            </div>

                            <div className="flex items-center space-x-2">
                                <Badge className={getStatusColor(agendamento.status)}>{getStatusLabel(agendamento.status)}</Badge>
                            </div>
                        </div>
                    </div>

                    <div className="space-y-6">
                        {/* Informações Principais */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Calendar className="mr-2 h-5 w-5" />
                                    Informações do Agendamento
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                    <div>
                                        <div className="mb-3 flex items-center text-sm text-gray-600">
                                            <Clock className="mr-2 h-4 w-4" />
                                            <span className="font-medium">Data e Hora:</span>
                                            <span className="ml-2">{formatDateTime((agendamento as any).scheduled_at)}</span>
                                        </div>
                                        <div className="mb-3 flex items-center text-sm text-gray-600">
                                            <MapPin className="mr-2 h-4 w-4" />
                                            <span className="font-medium">Endereço:</span>
                                            <span className="ml-2">{(agendamento as any).address}</span>
                                        </div>
                                        <div className="mb-3 flex items-center text-sm text-gray-600">
                                            <DollarSign className="mr-2 h-4 w-4" />
                                            <span className="font-medium">Valor:</span>
                                            <span className="ml-2 font-bold text-green-600">{formatCurrency((agendamento as any).price)}</span>
                                        </div>
                                    </div>

                                    <div>
                                        {agendamento.tempo_restante && (
                                            <div className="mb-3 flex items-center text-sm text-gray-600">
                                                <AlertTriangle className="mr-2 h-4 w-4" />
                                                <span className="font-medium">Tempo restante:</span>
                                                <span className="ml-2">{agendamento.tempo_restante}</span>
                                            </div>
                                        )}
                                        {agendamento.started_at && (
                                            <div className="mb-3 text-sm text-gray-600">
                                                <span className="font-medium">Iniciado em:</span>
                                                <span className="ml-2">{formatDateTime(agendamento.started_at)}</span>
                                            </div>
                                        )}
                                        {agendamento.finished_at && (
                                            <div className="mb-3 text-sm text-gray-600">
                                                <span className="font-medium">Finalizado em:</span>
                                                <span className="ml-2">{formatDateTime(agendamento.finished_at)}</span>
                                            </div>
                                        )}
                                        {(agendamento as any).cancellation_reason && (
                                            <div className="mb-3 text-sm text-gray-600">
                                                <span className="font-medium">Motivo do cancelamento:</span>
                                                <span className="ml-2">{(agendamento as any).cancellation_reason}</span>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {(agendamento as any).notes && (
                                    <div className="mt-6 border-t pt-6">
                                        <h4 className="mb-2 font-medium text-gray-900">Observações:</h4>
                                        <p className="text-gray-600">{(agendamento as any).notes}</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Informações do Paciente */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <User className="mr-2 h-5 w-5" />
                                    Informações do Paciente
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                    <div>
                                        <div className="mb-3 flex items-center text-sm text-gray-600">
                                            <User className="mr-2 h-4 w-4" />
                                            <span className="font-medium">Nome:</span>
                                            <span className="ml-2">{agendamento.paciente.name}</span>
                                        </div>
                                        <div className="mb-3 flex items-center text-sm text-gray-600">
                                            <Mail className="mr-2 h-4 w-4" />
                                            <span className="font-medium">Email:</span>
                                            <span className="ml-2">{agendamento.paciente.email}</span>
                                        </div>
                                        {agendamento.paciente.phone && (
                                            <div className="mb-3 flex items-center text-sm text-gray-600">
                                                <Phone className="mr-2 h-4 w-4" />
                                                <span className="font-medium">Telefone:</span>
                                                <span className="ml-2">{agendamento.paciente.phone}</span>
                                            </div>
                                        )}
                                    </div>

                                    <div className="flex justify-end">
                                        <Link href={route('fisioterapeuta.pacientes.show', agendamento.paciente.id)}>
                                            <Button variant="outline">
                                                <User className="mr-2 h-4 w-4" />
                                                Ver Perfil do Paciente
                                            </Button>
                                        </Link>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Relatório da Sessão */}
                        {agendamento.status === 'concluido' && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center">
                                        <FileText className="mr-2 h-5 w-5" />
                                        Relatório da Sessão
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    {(agendamento as any).relatorio ? (
                                        <div className="space-y-4">
                                            <div>
                                                <h4 className="mb-2 font-medium text-gray-900">Observações:</h4>
                                                <p className="text-gray-600">{(agendamento as any).relatorio.observacoes}</p>
                                            </div>

                                            <div>
                                                <h4 className="mb-2 font-medium text-gray-900">Exercícios Realizados:</h4>
                                                <p className="text-gray-600">{(agendamento as any).relatorio.exercicios}</p>
                                            </div>

                                            <div>
                                                <h4 className="mb-2 font-medium text-gray-900">Evolução:</h4>
                                                <p className="text-gray-600">{(agendamento as any).relatorio.evolucao}</p>
                                            </div>

                                            <div>
                                                <h4 className="mb-2 font-medium text-gray-900">Próximos Passos:</h4>
                                                <p className="text-gray-600">{(agendamento as any).relatorio.proximos_passos}</p>
                                            </div>

                                            <div className="border-t pt-4">
                                                <Link href={route('fisioterapeuta.relatorios.show', (agendamento as any).relatorio_sessao?.id)}>
                                                    <Button variant="outline">
                                                        <FileText className="mr-2 h-4 w-4" />
                                                        Ver Relatório Completo
                                                    </Button>
                                                </Link>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="py-8 text-center">
                                            <FileText className="mx-auto h-12 w-12 text-gray-400" />
                                            <h3 className="mt-4 text-lg font-medium text-gray-900">Relatório não preenchido</h3>
                                            <p className="mt-2 text-gray-500">O relatório desta sessão ainda não foi preenchido.</p>
                                            <div className="mt-4">
                                                <Link href={route('fisioterapeuta.relatorios.create', agendamento.id)}>
                                                    <Button>
                                                        <FileText className="mr-2 h-4 w-4" />
                                                        Preencher Relatório
                                                    </Button>
                                                </Link>
                                            </div>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        )}

                        {/* Ações */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Ações</CardTitle>
                                <CardDescription>Gerencie o status deste agendamento</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="flex flex-wrap gap-4">
                                    {agendamento.status === 'agendado' && (
                                        <Button onClick={handleConfirmar} className="bg-green-600 hover:bg-green-700">
                                            <CheckCircle className="mr-2 h-4 w-4" />
                                            Confirmar Agendamento
                                        </Button>
                                    )}

                                    {agendamento.status === 'confirmado' && agendamento.pode_iniciar && (
                                        <Button onClick={handleIniciar} className="bg-blue-600 hover:bg-blue-700">
                                            <Play className="mr-2 h-4 w-4" />
                                            Iniciar Sessão
                                        </Button>
                                    )}

                                    {agendamento.status === 'em_andamento' && (
                                        <Button onClick={handleFinalizar} className="bg-gray-600 hover:bg-gray-700">
                                            <Square className="mr-2 h-4 w-4" />
                                            Finalizar Sessão
                                        </Button>
                                    )}

                                    {['agendado', 'confirmado'].includes(agendamento.status) && (
                                        <Button
                                            variant="outline"
                                            onClick={handleCancelar}
                                            className="border-red-200 text-red-600 hover:border-red-300 hover:text-red-700"
                                        >
                                            <XCircle className="mr-2 h-4 w-4" />
                                            Cancelar Agendamento
                                        </Button>
                                    )}

                                    {agendamento.status === 'concluido' && !agendamento.relatorio_sessao && (
                                        <Link href={route('fisioterapeuta.relatorios.create', agendamento.id)}>
                                            <Button>
                                                <FileText className="mr-2 h-4 w-4" />
                                                Preencher Relatório
                                            </Button>
                                        </Link>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
