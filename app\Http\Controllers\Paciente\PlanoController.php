<?php

namespace App\Http\Controllers\Paciente;

use App\Http\Controllers\Controller;
use App\Models\Plano;
use App\Models\Assinatura;
use App\Models\Pagamento;
use App\Services\MercadoPagoService;
use App\Services\AffiliateTrackingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Inertia\Inertia;
use Carbon\Carbon;

class PlanoController extends Controller
{
    /**
     * Display patient's subscription management
     */
    public function index()
    {
        $user = Auth::user();

        // Assinatura atual (ativa, trialing ou com atualização pendente)
        $assinaturaAtiva = Assinatura::where('user_id', $user->id)
            ->whereIn('status', ['ativa', 'trialing', 'pending_update'])
            ->where('end_date', '>', now())
            ->with('plano')
            ->first();

        // Se não tiver assinatura ativa, redireciona para pagamentos
        if (!$assinaturaAtiva) {
            return redirect()->route('paciente.pagamentos.index');
        }

        // Se tiver assinatura ativa, mostra a página de gerenciamento
        // Prepare scheduled plan change info (if any)
        $planoAgendado = null;
        if (!empty($assinaturaAtiva->scheduled_new_plano_id)) {
            $p = Plano::find($assinaturaAtiva->scheduled_new_plano_id);
            if ($p) {
                $planoAgendado = [
                    'id' => $p->id,
                    'nome' => $p->name,
                    'preco' => (float) $p->price,
                    'sessoes_mes' => $p->sessions_per_month,
                ];
            }
        }

        return Inertia::render('paciente/plano', [
            'assinaturaAtual' => [
                'id' => $assinaturaAtiva->id,
                'status' => $assinaturaAtiva->status,
                'data_inicio' => optional($assinaturaAtiva->start_date)->toDateString(),
                'data_fim' => optional($assinaturaAtiva->end_date)->toDateString(),
                'sessoes_restantes' => max(0, ((optional($assinaturaAtiva->plano)->sessions_per_month ?? 0) - ($assinaturaAtiva->sessions_used ?? 0))),
                'valor_mensal' => (float) $assinaturaAtiva->monthly_price,
                'cancel_at_period_end' => (bool) $assinaturaAtiva->cancel_at_period_end,
                // Handle both Carbon instances and plain strings for scheduled_change_date
                'scheduled_change_date' => $assinaturaAtiva->scheduled_change_date
                    ? (\Carbon\Carbon::parse($assinaturaAtiva->scheduled_change_date)->toDateString())
                    : null,
                'scheduled_new_plano' => $planoAgendado,
                'plano' => [
                    'id' => optional($assinaturaAtiva->plano)->id,
                    'nome' => optional($assinaturaAtiva->plano)->name,
                    'descricao' => optional($assinaturaAtiva->plano)->description,
                    'preco' => (float) (optional($assinaturaAtiva->plano)->price ?? 0),
                    'sessoes_mes' => optional($assinaturaAtiva->plano)->sessions_per_month ?? 0,
                ],
            ],
            'planosDisponiveis' => Plano::ativos()
                ->whereIn('name', ['Plano Pessoal', 'Plano Empresarial'])
                ->orderBy('price', 'asc')
                ->get()
                ->unique('name')
                ->values()
                ->map(function ($plano) {
                    return [
                        'id' => $plano->id,
                        'nome' => $plano->name,
                        'descricao' => $plano->description,
                        'preco' => (float) $plano->price,
                        'sessoes_mes' => $plano->sessions_per_month,
                        'beneficios' => $plano->benefits ?? [],
                    ];
                }),
            'historicoAssinaturas' => Assinatura::where('user_id', $user->id)
                ->where('id', '!=', $assinaturaAtiva->id)
                ->with('plano')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($a) {
                    return [
                        'id' => $a->id,
                        'status' => $a->status,
                        'data_inicio' => optional($a->start_date)->toDateString(),
                        'data_fim' => optional($a->end_date)->toDateString(),
                        'plano' => [
                            'nome' => optional($a->plano)->name,
                        ],
                    ];
                }),
            'pagamentosPendentes' => Pagamento::where('status', 'pendente')
                ->whereHas('assinatura', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })
                ->with('assinatura.plano')
                ->orderBy('due_date', 'asc')
                ->get()
                ->map(function ($p) {
                    return [
                        'id' => $p->id,
                        'valor' => (float) $p->amount,
                        'data_vencimento' => optional($p->due_date)->toDateString(),
                        'status' => $p->status,
                        'forma_pagamento' => $p->method,
                    ];
                }),
            'proximoPagamento' => $this->getProximoPagamento($assinaturaAtiva),
            'statsUso' => $this->getStatsUso($assinaturaAtiva),
        ]);
    }

    /**
     * Change plan
     */
    public function change(Request $request)
    {
        $request->validate([
            'novo_plano_id' => 'required|exists:planos,id',
        ]);

        $user = Auth::user();
        $novoPlano = Plano::findOrFail($request->novo_plano_id);

        $assinaturaAtual = Assinatura::where('user_id', $user->id)
            ->whereIn('status', ['ativa', 'trialing', 'pending_update'])
            ->first();

        if (!$assinaturaAtual) {
            return back()->withErrors([
                'plano' => 'Nenhuma assinatura ativa encontrada.'
            ]);
        }

        // Check if already on the same plan
        if ($assinaturaAtual->plano_id == $novoPlano->id) {
            return back()->withErrors([
                'plano' => 'Você já está neste plano.'
            ]);
        }

        DB::beginTransaction();
        try {
            \Log::info('PlanoController.change: inicio', [
                'user_id' => $user->id,
                'assinatura_id' => optional($assinaturaAtual)->id,
                'plano_atual_id' => optional($assinaturaAtual)->plano_id,
                'novo_plano_id' => $novoPlano->id,
            ]);
            // Calculate proportional value for plan change
            $valorProporcional = $this->calcularValorProporcional($assinaturaAtual, $novoPlano);
            \Log::info('PlanoController.change: valorProporcional calculado', [
                'valorProporcional' => $valorProporcional,
            ]);
            
            // For downgrades, the value can be zero or negative (credit)
            $valorCobranca = max(0, $valorProporcional);
            $isDowngrade = (float) $novoPlano->price < (float) $assinaturaAtual->monthly_price;

            // Downgrade: schedule at period end, no payment/checkout now
            if ($isDowngrade) {
                // Build update payload conditionally to avoid exception if columns are absent
                // Do NOT change status here to respect DB CHECK constraint
                $payload = [];
                try {
                    if (\Schema::hasColumn('assinaturas', 'scheduled_new_plano_id')) {
                        $payload['scheduled_new_plano_id'] = $novoPlano->id;
                    }
                    if (\Schema::hasColumn('assinaturas', 'scheduled_change_date')) {
                        $changeDate = null;
                        if ($assinaturaAtual->end_date instanceof \Carbon\Carbon) {
                            $changeDate = $assinaturaAtual->end_date->toDateString();
                        } elseif (!empty($assinaturaAtual->end_date)) {
                            $changeDate = \Carbon\Carbon::parse($assinaturaAtual->end_date)->toDateString();
                        } else {
                            $changeDate = now()->toDateString();
                        }
                        $payload['scheduled_change_date'] = $changeDate;
                    }
                } catch (\Throwable $t) {
                    // ignore schema check failures
                }
                $assinaturaAtual->update($payload);
                \Log::info('PlanoController.change: downgrade agendado', $payload);
                DB::commit();
                return redirect()->route('paciente.plano.index')
                    ->with('success', 'Downgrade agendado para o próximo ciclo. O novo valor passará a valer em ' . optional($assinaturaAtual->end_date)->format('d/m/Y') . '.');
            }

            // Non-downgrade and no amount to charge: apply immediately
            if ($valorCobranca <= 0) {
                $this->aplicarMudancaPlano($assinaturaAtual, $novoPlano);
                \Log::info('PlanoController.change: plano aplicado imediatamente');
                DB::commit();
                return redirect()->route('paciente.plano.index')
                    ->with('success', 'Plano alterado com sucesso!');
            }

            // Create pending payment for upgrade
            $pagamento = Pagamento::create([
                'assinatura_id' => $assinaturaAtual->id,
                'amount' => $valorCobranca,
                'due_date' => now(),
                'status' => 'pendente',
                'description' => 'Mudança para o plano ' . $novoPlano->name,
                'metadata' => [
                    'tipo' => 'mudanca_plano',
                    'plano_anterior_id' => $assinaturaAtual->plano_id,
                    'novo_plano_id' => $novoPlano->id,
                    'valor_proporcional' => $valorProporcional
                ]
            ]);

            // Create Mercado Pago payment preference
            $mp = new MercadoPagoService();
            
            // Update subscription amount in Mercado Pago if subscription exists
            if (!empty($assinaturaAtual->mercadopago_subscription_id)) {
                try {
                    $mp->updateSubscriptionAmount($assinaturaAtual->mercadopago_subscription_id, (float) $novoPlano->price);
                } catch (\Throwable $t) {
                    \Log::warning('PlanoController.change: falha ao atualizar valor no MP (seguirá)', [
                        'error' => $t->getMessage(),
                    ]);
                }
            }
            
            $pref = $mp->createPreference([
                'title' => 'Mudança para o plano ' . $novoPlano->name,
                'description' => 'Cobrança proporcional de mudança de plano',
                'amount' => $valorCobranca,
                'payer' => [
                    'name' => $user->name,
                    'email' => $user->email,
                ],
                'external_reference' => $pagamento->id,
                'success_url' => route('paciente.checkout.success') . '?payment_id=' . $pagamento->id . '&external_reference=' . $pagamento->id,
                'failure_url' => route('paciente.checkout.failure') . '?payment_id=' . $pagamento->id . '&external_reference=' . $pagamento->id,
                'pending_url' => route('paciente.checkout.pending') . '?payment_id=' . $pagamento->id . '&external_reference=' . $pagamento->id,
                'notification_url' => route('mercadopago.webhook'),
            ]);

            if (!($pref['success'] ?? false)) {
                DB::rollBack();
                throw ValidationException::withMessages([
                    'plano' => $pref['message'] ?? 'Falha ao iniciar pagamento.'
                ]);
            }

            // Schedule plan change at period end for upgrade confirmation as well (do not change status)
            $payload = [];
            try {
                if (\Schema::hasColumn('assinaturas', 'scheduled_new_plano_id')) {
                    $payload['scheduled_new_plano_id'] = $novoPlano->id;
                }
                if (\Schema::hasColumn('assinaturas', 'scheduled_change_date')) {
                    $changeDate = null;
                    if ($assinaturaAtual->end_date instanceof \Carbon\Carbon) {
                        $changeDate = $assinaturaAtual->end_date->toDateString();
                    } elseif (!empty($assinaturaAtual->end_date)) {
                        $changeDate = \Carbon\Carbon::parse($assinaturaAtual->end_date)->toDateString();
                    } else {
                        $changeDate = now()->toDateString();
                    }
                    $payload['scheduled_change_date'] = $changeDate;
                }
            } catch (\Throwable $t) {
                // ignore schema check failures
            }
            if (!empty($payload)) {
                $assinaturaAtual->update($payload);
            }
            \Log::info('PlanoController.change: upgrade preferencia criada e agendamento definido', $payload);

            // Update payment with preference ID
            $pagamento->update([
                'external_id' => $pref['id'] ?? null,
                'gateway_response' => $pref
            ]);

            DB::commit();
            
            // Redirect to checkout
            $redirectUrl = $pref['init_point'] ?? null;
            if ($redirectUrl) {
                return \Inertia\Inertia::location($redirectUrl);
            }
            
            return redirect()->route('paciente.plano.index')
                ->with('info', 'Por favor, complete o pagamento para confirmar a mudança de plano.');
                
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Erro ao processar mudança de plano: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'novo_plano_id' => $novoPlano->id,
                'trace' => $e->getTraceAsString()
            ]);
            
            $msg = 'Ocorreu um erro ao processar a mudança de plano. Detalhe: ' . $e->getMessage();
            throw ValidationException::withMessages([
                'plano' => $msg
            ]);
        }
    }

    /**
     * Cancel current subscription at the end of the billing period (keeps access until end_date)
     */
    public function cancel(Request $request)
    {
        $user = Auth::user();

        $assinatura = Assinatura::where('user_id', $user->id)
            ->whereIn('status', ['ativa', 'trialing', 'pending_update'])
            ->where('end_date', '>', now())
            ->first();

        if (!$assinatura) {
            return redirect()->route('paciente.plano.index')
                ->with('info', 'Você não possui uma assinatura ativa.');
        }

        try {
            // Tenta cancelar no gateway (parar futuras cobranças), mas NÃO bloqueia o fluxo local em caso de erro
            $gatewayOk = true;
            if (!empty($assinatura->mercadopago_subscription_id)) {
                try {
                    $mp = new MercadoPagoService();
                    $gatewayOk = (bool) $mp->cancelSubscription($assinatura->mercadopago_subscription_id);
                    if (!$gatewayOk) {
                        \Log::warning('Falha ao cancelar no Mercado Pago, seguirá com cancelamento local no fim do ciclo', [
                            'user_id' => $user->id,
                            'assinatura_id' => $assinatura->id,
                            'mercadopago_subscription_id' => $assinatura->mercadopago_subscription_id,
                        ]);
                    }
                } catch (\Throwable $t) {
                    $gatewayOk = false;
                    \Log::warning('Exceção ao cancelar no Mercado Pago, seguirá com cancelamento local no fim do ciclo', [
                        'user_id' => $user->id,
                        'assinatura_id' => $assinatura->id,
                        'mercadopago_subscription_id' => $assinatura->mercadopago_subscription_id,
                        'error' => $t->getMessage(),
                    ]);
                }
            } else {
                \Log::info('Assinatura sem mercadopago_subscription_id. Apenas cancelamento local ao fim do ciclo.', [
                    'user_id' => $user->id,
                    'assinatura_id' => $assinatura->id,
                ]);
            }

            // Garante que a coluna exista (hotfix caso migração não tenha sido executada ainda)
            $this->ensureCancelAtColumn();

            // Sempre agenda cancelamento local ao fim do ciclo, independentemente do resultado do gateway
            $assinatura->update([
                'cancel_at_period_end' => true,
            ]);

            $redirect = redirect()->route('paciente.plano.index')
                ->with('success', 'Cancelamento agendado para o fim do ciclo. Você manterá acesso até ' . optional($assinatura->end_date)->format('d/m/Y') . '.');

            if (!$gatewayOk) {
                $redirect->with('warning', 'Não conseguimos parar a recorrência no Mercado Pago agora. Vamos tentar novamente automaticamente.');
            }

            return $redirect;
        } catch (\Throwable $e) {
            \Log::error('Erro ao agendar cancelamento de assinatura', [
                'user_id' => $user->id,
                'assinatura_id' => $assinatura->id,
                'error' => $e->getMessage(),
            ]);

            $msg = 'Não foi possível agendar o cancelamento. Tente novamente.';
            if (app()->environment('local')) {
                $has = null;
                $cols = [];
                try {
                    $has = Schema::hasColumn('assinaturas', 'cancel_at_period_end');
                    $list = Schema::getColumnListing('assinaturas');
                    // limita tamanho
                    $cols = array_slice($list, 0, 12);
                } catch (\Throwable $t) {
                    // ignore
                }
                $msg .= ' Detalhe: ' . $e->getMessage() . ' | hasColumn: ' . var_export($has, true) . ' | cols: ' . implode(',', $cols);
            }
            return redirect()->route('paciente.plano.index')
                ->with('error', $msg);
        }
    }

    /**
     * Reactivate a subscription scheduled to be canceled at period end
     */
    public function reactivate(Request $request)
    {
        $user = Auth::user();

        $assinatura = Assinatura::where('user_id', $user->id)
            ->whereIn('status', ['ativa', 'trialing', 'pending_update'])
            ->where('end_date', '>', now())
            ->first();

        if (!$assinatura) {
            return redirect()->route('paciente.plano.index')
                ->with('info', 'Nenhuma assinatura ativa encontrada.');
        }

        try {
            // Garante que a coluna exista (hotfix caso migração não tenha sido executada ainda)
            $this->ensureCancelAtColumn();

            $assinatura->update([
                'cancel_at_period_end' => false,
            ]);

            return redirect()->route('paciente.plano.index')
                ->with('success', 'Sua assinatura foi reativada.');
        } catch (\Throwable $e) {
            \Log::error('Erro ao reativar assinatura', [
                'user_id' => $user->id,
                'assinatura_id' => $assinatura->id,
                'error' => $e->getMessage(),
            ]);

            $msg = 'Não foi possível reativar sua assinatura. Tente novamente.';
            if (app()->environment('local')) {
                $msg .= ' Detalhe: ' . $e->getMessage();
            }
            return redirect()->route('paciente.plano.index')
                ->with('error', $msg);
        }
    }

    /**
     * Apply plan change
     */
    private function aplicarMudancaPlano($assinatura, $novoPlano)
    {
        $assinatura->update([
            'plano_id' => $novoPlano->id,
            'monthly_price' => $novoPlano->price,
            'scheduled_new_plano_id' => null,
            'scheduled_change_date' => null,
            'status' => 'ativa'
        ]);
    }

    /**
     * Cancel a scheduled plan change (keeps current plan)
     */
    public function cancelScheduledChange(Request $request)
    {
        $user = Auth::user();
        $assinatura = Assinatura::where('user_id', $user->id)
            ->whereIn('status', ['ativa', 'trialing', 'pending_update'])
            ->first();

        if (!$assinatura) {
            return redirect()->route('paciente.plano.index')
                ->with('info', 'Nenhuma assinatura ativa encontrada.');
        }

        try {
            $payload = [];
            try {
                if (Schema::hasColumn('assinaturas', 'scheduled_new_plano_id')) {
                    $payload['scheduled_new_plano_id'] = null;
                }
                if (Schema::hasColumn('assinaturas', 'scheduled_change_date')) {
                    $payload['scheduled_change_date'] = null;
                }
            } catch (\Throwable $t) {
                // ignore schema inspection errors
            }

            if (!empty($payload)) {
                $assinatura->update($payload);
            }

            \Log::info('PlanoController.cancelScheduledChange: mudança de plano agendada cancelada', [
                'user_id' => $user->id,
                'assinatura_id' => $assinatura->id,
            ]);

            return redirect()->route('paciente.plano.index')
                ->with('success', 'Mudança de plano agendada foi cancelada. Seu plano atual será mantido.');
        } catch (\Throwable $e) {
            \Log::error('Erro ao cancelar mudança de plano agendada', [
                'user_id' => $user->id,
                'assinatura_id' => optional($assinatura)->id,
                'error' => $e->getMessage(),
            ]);
            return redirect()->route('paciente.plano.index')
                ->with('error', 'Não foi possível cancelar a mudança de plano. Tente novamente.');
        }
    }

    /**
     * Get next payment
     */
    private function getProximoPagamento($assinatura)
    {
        if (!$assinatura) return null;
        
        $pagamento = Pagamento::where('assinatura_id', $assinatura->id)
            ->where('status', 'pendente')
            ->orderBy('due_date', 'asc')
            ->first();
            
        return $pagamento ? [
            'id' => $pagamento->id,
            'valor' => (float) $pagamento->amount,
            'data_vencimento' => optional($pagamento->due_date)->toDateString(),
            'forma_pagamento' => $pagamento->method,
        ] : null;
    }

    /**
     * Get usage statistics
     */
    private function getStatsUso($assinatura)
    {
        if (!$assinatura) {
            return [
                'sessoesUsadas' => 0,
                'sessoesRestantes' => 0,
                'percentualUso' => 0,
                'diasRestantes' => 0,
            ];
        }
        
        $totalMes = (int) ($assinatura->plano->sessions_per_month ?? 0);
        $usadas = (int) ($assinatura->sessions_used ?? 0);
        $sessoesRestantes = max(0, $totalMes - $usadas);
        $sessoesUsadas = min($usadas, $totalMes);
        $percentualUso = $totalMes > 0 ? ($sessoesUsadas / $totalMes) * 100 : 0;
        $diasRestantes = \Carbon\Carbon::now()->diffInDays($assinatura->end_date, false);

        return [
            'sessoesUsadas' => $sessoesUsadas,
            'sessoesRestantes' => $sessoesRestantes,
            'percentualUso' => round($percentualUso, 1),
            'diasRestantes' => (int) max(0, $diasRestantes),
        ];
    }
    
    /**
     * Calculate proportional value for plan change
     */
    private function calcularValorProporcional($assinaturaAtual, $novoPlano)
    {
        // Robust implementation: handle missing dates and negative intervals
        $now = \Carbon\Carbon::now();

        // Choose start/end with fallbacks
        $start = $assinaturaAtual->start_date
            ? \Carbon\Carbon::parse($assinaturaAtual->start_date)
            : ($assinaturaAtual->current_period_start
                ? \Carbon\Carbon::parse($assinaturaAtual->current_period_start)
                : $now->copy()->startOfMonth());

        $end = $assinaturaAtual->end_date
            ? \Carbon\Carbon::parse($assinaturaAtual->end_date)
            : ($assinaturaAtual->current_period_end
                ? \Carbon\Carbon::parse($assinaturaAtual->current_period_end)
                : $now->copy()->endOfMonth());

        // Ensure end is after start; fallback to 30-day window
        if ($end->lte($start)) {
            $end = $start->copy()->addDays(30);
        }

        $diasTotais = max(1, $start->diffInDays($end));
        $diasRestantes = $now->lt($end) ? $now->diffInDays($end) : 0;

        $valorRestanteAtual = ((float) ($assinaturaAtual->monthly_price ?? 0) / $diasTotais) * $diasRestantes;
        $valorNovoPlano = ((float) ($novoPlano->price ?? 0) / $diasTotais) * $diasRestantes;

        return $valorNovoPlano - $valorRestanteAtual;
    }

    /**
     * Hotfix: Ensure 'cancel_at_period_end' column exists on 'assinaturas' table.
     * Prefer running migrations, but this guards runtime if missing locally.
     */
    private function ensureCancelAtColumn(): void
    {
        try {
            if (!Schema::hasColumn('assinaturas', 'cancel_at_period_end')) {
                Schema::table('assinaturas', function (Blueprint $table) {
                    $table->boolean('cancel_at_period_end')->default(false)->after('status');
                });
                \Log::info("Coluna 'cancel_at_period_end' criada dinamicamente em 'assinaturas'.");
            }
        } catch (\Throwable $e) {
            \Log::error('Falha ao garantir coluna cancel_at_period_end: ' . $e->getMessage());
        }
    }

    /**
     * Hotfix: Ensure scheduled plan change columns exist on 'assinaturas'.
     * Prefer running migrations, but this prevents local errors.
     */
    private function ensureScheduledColumns(): void
    {
        try {
            $needsAlter = false;
            if (!Schema::hasColumn('assinaturas', 'scheduled_new_plano_id')) {
                $needsAlter = true;
            }
            if (!Schema::hasColumn('assinaturas', 'scheduled_change_date')) {
                $needsAlter = true;
            }
            if ($needsAlter) {
                Schema::table('assinaturas', function (Blueprint $table) {
                    if (!Schema::hasColumn('assinaturas', 'scheduled_new_plano_id')) {
                        $table->unsignedBigInteger('scheduled_new_plano_id')->nullable()->after('monthly_price');
                    }
                    if (!Schema::hasColumn('assinaturas', 'scheduled_change_date')) {
                        $table->dateTime('scheduled_change_date')->nullable()->after('scheduled_new_plano_id');
                    }
                });
                \Log::info("Colunas de agendamento de mudança de plano criadas dinamicamente em 'assinaturas'.");
            }
        } catch (\Throwable $e) {
            \Log::error('Falha ao garantir colunas de agendamento: ' . $e->getMessage());
        }
    }
}
