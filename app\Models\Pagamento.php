<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Pagamento extends Model
{
    use HasFactory;

    protected $fillable = [
        'assinatura_id',
        'agendamento_id',
        'user_id',
        'amount',
        'status',
        'method',
        'transaction_id',
        'preference_id',
        'payment_id',
        'due_date',
        'paid_at',
        'notes',
        'gateway_response',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'due_date' => 'date',
        'paid_at' => 'datetime',
        'gateway_response' => 'array',
    ];

    // Relacionamentos
    public function assinatura()
    {
        return $this->belongsTo(Assinatura::class);
    }

    public function agendamento()
    {
        return $this->belongsTo(Agendamento::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopePagos($query)
    {
        return $query->where('status', 'pago');
    }

    public function scopePendentes($query)
    {
        return $query->where('status', 'pendente');
    }

    public function scopeVencidos($query)
    {
        return $query->where('status', 'pendente')
                    ->where('due_date', '<', now());
    }

    // Métodos auxiliares
    public function getFormattedAmountAttribute()
    {
        return 'R$ ' . number_format($this->amount, 2, ',', '.');
    }

    public function getFormattedDueDateAttribute()
    {
        return $this->due_date->format('d/m/Y');
    }

    public function getFormattedPaidAtAttribute()
    {
        return $this->paid_at ? $this->paid_at->format('d/m/Y H:i') : null;
    }

    public function isVencido()
    {
        return $this->status === 'pendente' && $this->due_date < now();
    }

    public function isPago()
    {
        return $this->status === 'pago';
    }

    public function isPendente()
    {
        return $this->status === 'pendente';
    }
}
