<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Agendamento extends Model
{
    use HasFactory;

    protected $fillable = [
        'paciente_id',
        'fisioterapeuta_id',
        'assinatura_id',
        'scheduled_at',
        'duration',
        'status',
        'service_type',
        'appointment_type',
        'appointment_type_notes',
        'notes',
        'address',
        'address_line2',
        'address_city',
        'address_state',
        'address_zip_code',
        'price',
        'started_at',
        'finished_at',
        'cancellation_reason',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'started_at' => 'datetime',
        'finished_at' => 'datetime',
        'price' => 'decimal:2',
    ];

    // Relacionamentos
    public function paciente()
    {
        return $this->belongsTo(User::class, 'paciente_id');
    }

    public function fisioterapeuta()
    {
        return $this->belongsTo(Fisioterapeuta::class, 'fisioterapeuta_id', 'user_id');
    }

    public function assinatura()
    {
        return $this->belongsTo(Assinatura::class);
    }

    public function relatorioSessao()
    {
        return $this->hasOne(RelatorioSessao::class);
    }

    public function avaliacao()
    {
        return $this->hasOne(Avaliacao::class);
    }

    public function pagamento()
    {
        return $this->hasOne(Pagamento::class, 'agendamento_id');
    }

    // Scopes
    public function scopeAgendados($query)
    {
        return $query->where('status', 'agendado');
    }

    public function scopeConcluidos($query)
    {
        return $query->where('status', 'concluido');
    }

    public function scopePendentes($query)
    {
        return $query->where('status', 'pendente');
    }

    public function scopeConfirmados($query)
    {
        return $query->where('status', 'confirmado');
    }

    public function scopeACaminho($query)
    {
        return $query->where('status', 'a caminho');
    }

    // Métodos para verificação de pagamento
    public function isPendente()
    {
        return $this->status === 'pendente';
    }

    public function isConfirmado()
    {
        return $this->status === 'confirmado';
    }

    public function isACaminho()
    {
        return $this->status === 'a caminho';
    }

    public function hasPayment()
    {
        return $this->pagamento !== null;
    }

    public function isPaymentConfirmed()
    {
        if (!$this->hasPayment()) {
            return false;
        }
        
        return $this->pagamento->status === 'pago';
    }

    public function getPaymentLink()
    {
        if (!$this->hasPayment()) {
            return null;
        }

        // Verificar tanto preference_id quanto transaction_id para compatibilidade
        $preferenceId = $this->pagamento->preference_id ?? $this->pagamento->transaction_id;
        
        if (!$preferenceId) {
            return null;
        }

        try {
            // Usar o método existente no TestePagamentoController como referência
            $mercadoPagoService = app(\App\Services\MercadoPagoService::class);
            $baseUrl = config('mercadopago.base_url');
            $accessToken = $mercadoPagoService->getAccessToken();
            
            $httpClient = \Illuminate\Support\Facades\Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
            ]);

            // Desabilitar verificação SSL em desenvolvimento
            $disableSslVerify = app()->environment('local', 'development') || env('MERCADOPAGO_DISABLE_SSL_VERIFY', false);
            if ($disableSslVerify) {
                $httpClient = $httpClient->withOptions(['verify' => false]);
            }

            $response = $httpClient->get($baseUrl . '/checkout/preferences/' . $preferenceId);

            if ($response->successful()) {
                $preferenceData = $response->json();
                return $preferenceData['init_point'] ?? null;
            }
            
            return null;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Erro ao obter link de pagamento: ' . $e->getMessage());
            return null;
        }
    }

    public function verificarEConfirmarPagamento()
    {
        if (!$this->hasPayment()) {
            return false;
        }

        try {
            $paymentService = app(\App\Services\UnifiedPaymentService::class);
            
            // Se já tem payment_id, verificar diretamente
            if ($this->pagamento->payment_id) {
                $paymentInfo = $paymentService->getPaymentInfo($this->pagamento->payment_id);
                if ($paymentInfo) {
                    $paymentService->processWebhook($paymentInfo['id']);
                }
            } else {
                // Buscar pagamentos pelo external_reference (ID do pagamento)
                $paymentInfo = $this->buscarPagamentosPorExternalReference($this->pagamento->id);
                if ($paymentInfo) {
                    $paymentService->processWebhook($paymentInfo['id']);
                }
            }

            // Recarregar o relacionamento para obter os dados atualizados
            $this->load('pagamento');

            // Se o pagamento foi confirmado, atualizar o status do agendamento
            if ($this->isPaymentConfirmed() && $this->isPendente()) {
                $this->update(['status' => 'agendado']);

                return true;
            }

            return false;

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Erro ao verificar pagamento do agendamento: ' . $e->getMessage(), [
                'agendamento_id' => $this->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    private function buscarPagamentosPorExternalReference($externalReference)
    {
        if (!$externalReference) {
            return null;
        }

        try {
            $mercadoPagoService = app(\App\Services\MercadoPagoService::class);
            $baseUrl = config('mercadopago.base_url');
            $accessToken = $mercadoPagoService->getAccessToken();
            
            $httpClient = \Illuminate\Support\Facades\Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
            ]);

            // Desabilitar verificação SSL em desenvolvimento
            $disableSslVerify = app()->environment('local', 'development') || env('MERCADOPAGO_DISABLE_SSL_VERIFY', false);
            if ($disableSslVerify) {
                $httpClient = $httpClient->withOptions(['verify' => false]);
            }

            $response = $httpClient->get($baseUrl . '/v1/payments/search', [
                'external_reference' => $externalReference,
                'limit' => 10
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $pagamentos = $data['results'] ?? [];
                
                if (!empty($pagamentos)) {
                    // Ordenar por data de criação (mais recente primeiro)
                    usort($pagamentos, function($a, $b) {
                        $dateA = strtotime($a['date_created'] ?? 0);
                        $dateB = strtotime($b['date_created'] ?? 0);
                        return $dateB <=> $dateA;
                    });
                    
                    return $pagamentos[0]; // Retornar o pagamento mais recente
                }
            }
            
            return null;
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Erro ao buscar pagamentos por external_reference: ' . $e->getMessage());
            return null;
        }
    }
}
