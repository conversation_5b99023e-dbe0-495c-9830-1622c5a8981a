// removed Alert components (no plan messaging)
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { DatePicker } from '@/components/ui/date-picker';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { cn } from '@/lib/utils';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router, useForm, usePage } from '@inertiajs/react';
import { format } from 'date-fns';
import { AlertTriangle, ArrowLeft, CheckCircle, ChevronDown, Clock, Star, User } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';

interface Fisioterapeuta {
    id: number;
    user: {
        name: string;
        avatar?: string;
    };
    crefito: string;
    specializations: string[];
    hourly_rate: number;
    travel_fee?: number | null;
    service_rates?: {
        avaliacao?: number | null;
        sessao?: number | null;
        teleatendimento?: number | null;
    } | null;
    rating: number;
    total_reviews: number;
}

// Removed Props interface (pageProps is used directly)

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Início', href: '/paciente/dashboard' },
    { title: 'Agendamentos', href: '/paciente/agendamentos' },
    { title: 'Novo', href: '' },
];

export default function PacienteAgendamentoCreate() {
    // Página ativa: renderiza formulário de criação de agendamento

    const pageProps = usePage<any>().props;
    const { fisioterapeutas, fisioterapeutaSelecionado, horariosDisponiveis: initialHorariosDisponiveis } = pageProps;

    const [loadingHorarios, setLoadingHorarios] = useState(false);
    const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
    const [fisioterapeutaOpen, setFisioterapeutaOpen] = useState(false);
    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [horariosDinamicos, setHorariosDinamicos] = useState<string[]>([]);

    const { data, setData, processing, errors } = useForm({
        fisioterapeuta_id: fisioterapeutaSelecionado?.id.toString() || '',
        data_agendamento: '',
        horario: '',
        tipo: 'sessao',
        appointment_type: 'domicilio', // Atendimento domiciliar por padrão
        appointment_type_notes: '',
        observacoes: '',
        data_hora: '',
        // Campos de endereço
        address: pageProps.user?.address || '',
        address_line2: pageProps.user?.address_line2 || '',
        address_city: pageProps.user?.city || '',
        address_state: pageProps.user?.state || '',
        address_zip_code: pageProps.user?.zip_code || '',
    });

    // Lógica para determinar quais horários usar - sempre priorizar dinâmicos quando disponíveis
    // Se há fisioterapeuta e data selecionados, usar horários dinâmicos (mesmo que vazio)
    // Caso contrário, usar horários iniciais como fallback
    const horariosExibidos = data.fisioterapeuta_id && data.data_agendamento ? horariosDinamicos : initialHorariosDisponiveis;

    // Currency formatter and selected fisioterapeuta resolution
    const nf = useMemo(() => new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }), []);
    const selectedFisio: Fisioterapeuta | undefined = useMemo(() => {
        if (data.fisioterapeuta_id) {
            return fisioterapeutas.find((f: Fisioterapeuta) => f.id.toString() === data.fisioterapeuta_id);
        }
        return fisioterapeutaSelecionado;
    }, [data.fisioterapeuta_id, fisioterapeutas, fisioterapeutaSelecionado]);

    // Sincronizar data inicial se houver uma pré-selecionada
    useEffect(() => {
        if (data.data_agendamento && !selectedDate) {
            setSelectedDate(new Date(data.data_agendamento + 'T00:00:00'));
        }
    }, [data.data_agendamento, selectedDate]);

    // Efeito para carregar horários disponíveis dinamicamente
    useEffect(() => {
        const carregarHorarios = async () => {
            if (data.fisioterapeuta_id && data.data_agendamento && selectedDate) {
                setLoadingHorarios(true);
                try {
                    const response = await fetch(
                        route('horarios-disponiveis', {
                            fisioterapeuta_id: data.fisioterapeuta_id,
                            data: data.data_agendamento,
                        }),
                        {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest',
                            },
                        },
                    );

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const result = await response.json();

                    if (result.success) {
                        console.log('Horários disponíveis recebidos:', result.horarios);
                        setHorariosDinamicos(result.horarios);

                        // Reset horário se não estiver mais disponível
                        if (data.horario && !result.horarios.includes(data.horario)) {
                            console.log('Horário anterior não disponível, resetando:', data.horario);
                            setData('horario', '');
                        }
                    } else {
                        console.error('Erro no servidor:', result.error || 'Erro desconhecido');
                        setHorariosDinamicos([]);
                    }
                } catch (error) {
                    console.error('Erro ao carregar horários:', error);
                    // Em caso de erro, limpar horários dinâmicos
                    setHorariosDinamicos([]);
                } finally {
                    setLoadingHorarios(false);
                }
            } else {
                // Se não há fisioterapeuta ou data, limpar horários dinâmicos
                setHorariosDinamicos([]);
            }
        };

        // Pequeno delay para evitar múltiplas requisições
        const timeoutId = setTimeout(carregarHorarios, 300);
        return () => clearTimeout(timeoutId);
    }, [data.fisioterapeuta_id, data.data_agendamento, selectedDate]);

    // Função para lidar com mudança de data
    const handleDateChange = (date: Date | undefined) => {
        setSelectedDate(date);
        if (date) {
            const dateString = format(date, 'yyyy-MM-dd');
            setData('data_agendamento', dateString);
        } else {
            setData('data_agendamento', '');
        }
        // Reset horário quando data muda
        setData('horario', '');
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (isSubmitting) return;

        // Limpar erros anteriores
        setValidationErrors({});

        // Validações do frontend
        const newValidationErrors: Record<string, string> = {};

        if (!data.fisioterapeuta_id) {
            newValidationErrors.fisioterapeuta_id = 'Selecione um fisioterapeuta';
        }

        if (!data.data_agendamento) {
            newValidationErrors.data_agendamento = 'Selecione uma data';
        } else {
            const selectedDate = new Date(data.data_agendamento + 'T00:00:00');
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (selectedDate <= today) {
                newValidationErrors.data_agendamento = 'A data deve ser futura';
            }

            if (selectedDate.getDay() === 0) {
                newValidationErrors.data_agendamento = 'Não é possível agendar aos domingos';
            }

            // Verificar se a data não é muito distante (máximo 60 dias)
            const maxDate = new Date();
            maxDate.setDate(maxDate.getDate() + 60);
            if (selectedDate > maxDate) {
                newValidationErrors.data_agendamento = 'Não é possível agendar com mais de 60 dias de antecedência';
            }
        }

        if (!data.horario) {
            newValidationErrors.horario = 'Selecione um horário';
        }

        if (!data.tipo) {
            newValidationErrors.tipo = 'Selecione o tipo de agendamento';
        }

        // Verificar se o horário ainda está disponível (validação adicional)
        if (data.horario && !horariosExibidos.includes(data.horario)) {
            newValidationErrors.horario = 'Este horário não está disponível. Selecione outro horário.';
        }

        // Verificar se o horário existe nos horários dinâmicos (se houver)
        if (data.horario && horariosDinamicos.length > 0 && !horariosDinamicos.includes(data.horario)) {
            newValidationErrors.horario = 'Este horário não está disponível para a data selecionada.';
        }

        // Se há erros, mostrar e não submeter
        if (Object.keys(newValidationErrors).length > 0) {
            setValidationErrors(newValidationErrors);
            return;
        }

        // Combinar data e horário para enviar ao backend
        const dataHora = `${data.data_agendamento} ${data.horario}:00`;

        setIsSubmitting(true);

        // Criar payload com data_hora combinada
        const payload = {
            fisioterapeuta_id: data.fisioterapeuta_id,
            data_agendamento: data.data_agendamento,
            horario: data.horario,
            tipo: data.tipo,
            appointment_type: data.appointment_type,
            appointment_type_notes: data.appointment_type_notes,
            observacoes: data.observacoes,
            data_hora: dataHora,
            // Campos de endereço
            address: data.address,
            address_line2: data.address_line2,
            address_city: data.address_city,
            address_state: data.address_state,
            address_zip_code: data.address_zip_code,
        };

        // Usar router.post diretamente com payload
        router.post(route('paciente.agendamentos.store'), payload, {
            onFinish: () => {
                setIsSubmitting(false);
            },
            onError: () => {
                setIsSubmitting(false);
            },
        });
    };

    // Função para desabilitar datas passadas, domingos e dias sem horários configurados
    const isDateDisabled = (date: Date) => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const isPast = date < today;
        const isSunday = date.getDay() === 0;

        // Se não há fisioterapeuta selecionado, apenas desabilitar passado e domingos
        if (!fisioterapeutaSelecionado) {
            return isPast || isSunday;
        }

        // Verificar se o fisioterapeuta trabalha neste dia da semana
        const dayOfWeek = date.getDay(); // 0=domingo, 1=segunda, 2=terça, etc.

        const hasScheduleForDay =
            (fisioterapeutaSelecionado as any).horarios_base?.some((horario: any) => horario.dia_semana === dayOfWeek && horario.ativo) || false;

        return isPast || isSunday || !hasScheduleForDay;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Novo Agendamento" />

            <div className="mx-auto w-full max-w-7xl space-y-6 px-4 py-6 sm:px-6 lg:px-8">
                <div className="mb-8">
                    <div className="mb-4">
                        <Link href={route('paciente.agendamentos.index')}>
                            <Button variant="ghost" size="sm">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Voltar aos Agendamentos
                            </Button>
                        </Link>
                    </div>
                    <h1 className="text-2xl font-bold tracking-tight sm:text-3xl">Novo Agendamento</h1>
                    <p className="text-muted-foreground">Agende sua próxima sessão de fisioterapia</p>
                </div>

                {/* Formulário de agendamento */}
                <Card>
                    <CardHeader>
                        <CardTitle>Dados do Agendamento</CardTitle>
                    </CardHeader>
                    <CardContent>
                        {!selectedFisio ? (
                            <div className="flex flex-col items-center justify-center gap-4 py-12 text-center">
                                <p className="text-muted-foreground">Selecione um fisioterapeuta para continuar o agendamento.</p>
                                <Link href={route('paciente.fisioterapeutas.index')}>
                                    <Button>
                                        <User className="mr-2 h-4 w-4" /> Buscar Fisioterapeutas
                                    </Button>
                                </Link>
                            </div>
                        ) : (
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Tipo de Agendamento */}
                                <div className="space-y-2">
                                    <Label htmlFor="tipo">Tipo de Agendamento</Label>
                                    <Select value={data.tipo} onValueChange={(value) => setData('tipo', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Selecione o tipo" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="sessao">Sessão de Fisioterapia</SelectItem>
                                            <SelectItem value="avaliacao">Avaliação</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.tipo && <p className="text-sm text-red-600">{errors.tipo}</p>}
                                </div>

                                {/* Fisioterapeuta Selecionado */}
                                {fisioterapeutaSelecionado && (
                                    <Card className="border-green-200 bg-green-50">
                                        <CardHeader>
                                            <CardTitle className="flex items-center gap-2 text-green-800">
                                                <CheckCircle className="h-5 w-5" />
                                                Fisioterapeuta Selecionado
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="flex items-start gap-4">
                                                <Avatar className="h-16 w-16">
                                                    <AvatarImage src={fisioterapeutaSelecionado.user.avatar} />
                                                    <AvatarFallback>
                                                        {fisioterapeutaSelecionado.user.name
                                                            .split(' ')
                                                            .map((n: string) => n[0])
                                                            .join('')
                                                            .toUpperCase()}
                                                    </AvatarFallback>
                                                </Avatar>

                                                <div className="flex-1">
                                                    <h3 className="text-lg font-semibold">{fisioterapeutaSelecionado.user.name}</h3>
                                                    <p className="text-sm text-muted-foreground">CREFITO: {fisioterapeutaSelecionado.crefito}</p>

                                                    <div className="mt-1 flex items-center gap-1">
                                                        {Array.from({ length: 5 }, (_, i) => (
                                                            <Star
                                                                key={i}
                                                                className={`h-4 w-4 ${
                                                                    i < Math.floor(fisioterapeutaSelecionado.rating)
                                                                        ? 'fill-yellow-400 text-yellow-400'
                                                                        : 'text-gray-300'
                                                                }`}
                                                            />
                                                        ))}
                                                        <span className="ml-1 text-sm text-muted-foreground">
                                                            ({fisioterapeutaSelecionado.total_reviews} avaliações)
                                                        </span>
                                                    </div>

                                                    <div className="mt-2 flex flex-wrap gap-1">
                                                        {Array.isArray(fisioterapeutaSelecionado.specializations) &&
                                                            fisioterapeutaSelecionado.specializations.slice(0, 3).map((spec: string) => (
                                                                <Badge key={spec} variant="secondary" className="text-xs">
                                                                    {spec}
                                                                </Badge>
                                                            ))}
                                                    </div>

                                                    <div className="mt-2 flex flex-col gap-1 text-sm">
                                                        <div className="flex items-center gap-2">
                                                            <Clock className="h-4 w-4 text-muted-foreground" />
                                                            <span className="font-medium">
                                                                R$ {Number(fisioterapeutaSelecionado.hourly_rate || 0).toFixed(2)}/hora
                                                            </span>
                                                        </div>
                                                        {/* Sem taxa de deslocamento exibida para o paciente */}
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="mt-4 flex gap-2">
                                                <Link href={route('paciente.fisioterapeutas.show', fisioterapeutaSelecionado.id)}>
                                                    <Button variant="outline" size="sm">
                                                        Ver Perfil Completo
                                                    </Button>
                                                </Link>
                                                <Button variant="outline" size="sm" onClick={() => setData('fisioterapeuta_id', '')}>
                                                    Escolher Outro
                                                </Button>
                                            </div>
                                        </CardContent>
                                    </Card>
                                )}

                                {/* Seleção de Fisioterapeuta */}
                                {!fisioterapeutaSelecionado && (
                                    <div className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <Label htmlFor="fisioterapeuta_id">Escolher Fisioterapeuta</Label>
                                            <Link href={route('paciente.fisioterapeutas.index')}>
                                                <Button variant="outline" size="sm">
                                                    <User className="mr-2 h-4 w-4" />
                                                    Buscar Fisioterapeutas
                                                </Button>
                                            </Link>
                                        </div>

                                        <Popover open={fisioterapeutaOpen} onOpenChange={setFisioterapeutaOpen}>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    role="combobox"
                                                    aria-expanded={fisioterapeutaOpen}
                                                    className="w-full justify-between border-input bg-background px-3 font-normal outline-offset-0 outline-none hover:bg-background focus-visible:outline-[3px]"
                                                >
                                                    <span className={cn('truncate', !data.fisioterapeuta_id && 'text-muted-foreground')}>
                                                        {data.fisioterapeuta_id
                                                            ? fisioterapeutas.find((fisio: any) => fisio.id.toString() === data.fisioterapeuta_id)
                                                                  ?.user.name
                                                            : 'Selecione um fisioterapeuta'}
                                                    </span>
                                                    <ChevronDown size={16} className="shrink-0 text-muted-foreground/80" aria-hidden="true" />
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent
                                                className="w-full min-w-[var(--radix-popper-anchor-width)] border-input p-0"
                                                align="start"
                                            >
                                                <Command>
                                                    <CommandInput placeholder="Buscar fisioterapeuta..." />
                                                    <CommandList>
                                                        <CommandEmpty>Nenhum fisioterapeuta encontrado.</CommandEmpty>
                                                        <CommandGroup>
                                                            {fisioterapeutas.map((fisio: Fisioterapeuta) => (
                                                                <CommandItem
                                                                    key={fisio.id}
                                                                    value={`${fisio.user.name} ${fisio.crefito} ${Array.isArray(fisio.specializations) ? fisio.specializations.join(' ') : ''}`}
                                                                    onSelect={() => {
                                                                        setData('fisioterapeuta_id', fisio.id.toString());
                                                                        setFisioterapeutaOpen(false);
                                                                    }}
                                                                    className="flex items-center gap-3 py-3"
                                                                >
                                                                    <Avatar className="h-8 w-8">
                                                                        <AvatarImage src={fisio.user.avatar} />
                                                                        <AvatarFallback>
                                                                            {fisio.user.name
                                                                                .split(' ')
                                                                                .map((n: string) => n[0])
                                                                                .join('')
                                                                                .toUpperCase()}
                                                                        </AvatarFallback>
                                                                    </Avatar>
                                                                    <div className="flex-1">
                                                                        <div className="font-medium">{fisio.user.name}</div>
                                                                        <div className="text-xs text-muted-foreground">
                                                                            {Array.isArray(fisio.specializations)
                                                                                ? fisio.specializations.slice(0, 2).join(', ')
                                                                                : ''}
                                                                            {Array.isArray(fisio.specializations) &&
                                                                                fisio.specializations.length > 2 &&
                                                                                ` +${fisio.specializations.length - 2}`}
                                                                        </div>
                                                                        <div className="mt-1 flex items-center gap-1">
                                                                            {Array.from({ length: 5 }, (_, i) => (
                                                                                <Star
                                                                                    key={i}
                                                                                    className={`h-3 w-3 ${
                                                                                        i < Math.floor(fisio.rating)
                                                                                            ? 'fill-yellow-400 text-yellow-400'
                                                                                            : 'text-gray-300'
                                                                                    }`}
                                                                                />
                                                                            ))}
                                                                            <span className="ml-1 text-xs text-muted-foreground">
                                                                                R$ {Number(fisio.hourly_rate || 0).toFixed(2)}/hora
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                    {data.fisioterapeuta_id === fisio.id.toString() && (
                                                                        <CheckCircle size={16} className="ml-auto text-primary" />
                                                                    )}
                                                                </CommandItem>
                                                            ))}
                                                        </CommandGroup>
                                                    </CommandList>
                                                </Command>
                                            </PopoverContent>
                                        </Popover>
                                        {errors.fisioterapeuta_id && <p className="text-sm text-red-600">{errors.fisioterapeuta_id}</p>}
                                    </div>
                                )}

                                {/* Data */}
                                <div className="space-y-2">
                                    <Label htmlFor="data_agendamento">Data do Agendamento</Label>
                                    <DatePicker
                                        value={selectedDate}
                                        onValueChange={handleDateChange}
                                        placeholder="Selecione uma data"
                                        disabledDates={isDateDisabled}
                                    />
                                    {(errors.data_agendamento || validationErrors.data_agendamento) && (
                                        <p className="text-sm text-red-600">{errors.data_agendamento || validationErrors.data_agendamento}</p>
                                    )}
                                </div>

                                {/* Horário */}
                                <div className="space-y-4">
                                    <Label htmlFor="horario">Horário Disponível</Label>

                                    {!data.fisioterapeuta_id || !data.data_agendamento ? (
                                        <div className="flex items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-8">
                                            <div className="text-center text-muted-foreground">
                                                <Clock className="mx-auto mb-2 h-8 w-8" />
                                                <p>Selecione fisioterapeuta e data primeiro</p>
                                            </div>
                                        </div>
                                    ) : loadingHorarios ? (
                                        <div className="flex items-center justify-center p-8">
                                            <div className="text-center">
                                                <div className="mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
                                                <p className="text-muted-foreground">Carregando horários disponíveis...</p>
                                            </div>
                                        </div>
                                    ) : horariosExibidos.length === 0 ? (
                                        <div className="flex items-center justify-center rounded-lg border-2 border-dashed border-red-300 bg-red-50 p-8">
                                            <div className="text-center text-red-600">
                                                <AlertTriangle className="mx-auto mb-2 h-8 w-8" />
                                                <p className="font-medium">Nenhum horário disponível</p>
                                                <p className="text-sm">Tente selecionar outra data</p>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="space-y-3">
                                            <p className="text-sm text-muted-foreground">Selecione um dos horários disponíveis:</p>
                                            <div className="grid grid-cols-3 gap-2 sm:grid-cols-4 md:grid-cols-6">
                                                {horariosExibidos.map((horario: string) => (
                                                    <Button
                                                        key={horario}
                                                        type="button"
                                                        variant={data.horario === horario ? 'default' : 'outline'}
                                                        size="sm"
                                                        className="flex h-14 flex-col items-center justify-center gap-1 text-center"
                                                        onClick={() => setData('horario', horario)}
                                                    >
                                                        <Clock className="h-4 w-4" />
                                                        <span className="text-sm leading-none font-medium">{horario}</span>
                                                    </Button>
                                                ))}
                                            </div>

                                            {data.horario && (
                                                <div className="flex items-center gap-2 rounded-lg border border-green-200 bg-green-50 p-3">
                                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                                    <span className="text-sm text-green-800">
                                                        Horário selecionado: <strong>{data.horario}</strong>
                                                    </span>
                                                </div>
                                            )}
                                        </div>
                                    )}

                                    {(errors.horario || validationErrors.horario) && (
                                        <p className="text-sm text-red-600">{errors.horario || validationErrors.horario}</p>
                                    )}
                                </div>

                                {/* Local do Atendimento */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Local do Atendimento</CardTitle>
                                        <CardDescription>Informe o endereço onde será realizada a sessão</CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="space-y-4">
                                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                                <div className="space-y-2">
                                                    <Label htmlFor="address">Logradouro *</Label>
                                                    <Input
                                                        id="address"
                                                        placeholder="Rua, Avenida, etc."
                                                        value={data.address}
                                                        onChange={(e) => setData('address', e.target.value)}
                                                    />
                                                    {errors.address && <p className="text-sm text-red-600">{errors.address}</p>}
                                                </div>
                                                <div className="space-y-2">
                                                    <Label htmlFor="address_line2">Complemento</Label>
                                                    <Input
                                                        id="address_line2"
                                                        placeholder="Apartamento, bloco, sala..."
                                                        value={data.address_line2}
                                                        onChange={(e) => setData('address_line2', e.target.value)}
                                                    />
                                                </div>
                                                <div className="space-y-2">
                                                    <Label htmlFor="address_city">Cidade *</Label>
                                                    <Input
                                                        id="address_city"
                                                        placeholder="Nome da cidade"
                                                        value={data.address_city}
                                                        onChange={(e) => setData('address_city', e.target.value)}
                                                    />
                                                    {errors.address_city && <p className="text-sm text-red-600">{errors.address_city}</p>}
                                                </div>
                                                <div className="grid grid-cols-2 gap-2">
                                                    <div className="space-y-2">
                                                        <Label htmlFor="address_state">Estado *</Label>
                                                        <Input
                                                            id="address_state"
                                                            placeholder="EX"
                                                            value={data.address_state}
                                                            onChange={(e) => setData('address_state', e.target.value)}
                                                            maxLength={2}
                                                        />
                                                        {errors.address_state && <p className="text-sm text-red-600">{errors.address_state}</p>}
                                                    </div>
                                                    <div className="space-y-2">
                                                        <Label htmlFor="address_zip_code">CEP *</Label>
                                                        <Input
                                                            id="address_zip_code"
                                                            placeholder="00000-000"
                                                            value={data.address_zip_code}
                                                            onChange={(e) => setData('address_zip_code', e.target.value)}
                                                        />
                                                        {errors.address_zip_code && <p className="text-sm text-red-600">{errors.address_zip_code}</p>}
                                                    </div>
                                                </div>
                                            </div>

                                            {/* Nota informativa */}
                                            <div className="rounded-lg border border-muted bg-muted/50 p-3">
                                                <p className="text-sm text-muted-foreground">
                                                    Este endereço será usado para o atendimento domiciliar. Você pode editá-lo caso necessário.
                                                </p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Observações */}
                                <div className="space-y-2">
                                    <Label htmlFor="observacoes">Observações (opcional)</Label>
                                    <Textarea
                                        id="observacoes"
                                        placeholder="Descreva sintomas, dúvidas ou informações importantes..."
                                        value={data.observacoes}
                                        onChange={(e) => setData('observacoes', e.target.value)}
                                        rows={4}
                                    />
                                    {errors.observacoes && <p className="text-sm text-red-600">{errors.observacoes}</p>}
                                </div>

                                {/* Sem blocos de assinatura/planos */}

                                {/* Resumo de Preço */}
                                {selectedFisio && (
                                    <div className="rounded-lg border border-emerald-200 bg-emerald-50 p-4">
                                        <div className="space-y-1 text-emerald-900">
                                            <div className="flex items-center justify-between text-sm">
                                                <span>Valor por hora</span>
                                                <span className="font-medium">{nf.format(selectedFisio.hourly_rate || 0)}</span>
                                            </div>
                                            <p className="mt-1 text-xs text-emerald-700">
                                                Custos adicionais (ex.: deslocamento) são de responsabilidade do paciente.
                                            </p>
                                        </div>
                                    </div>
                                )}

                                <div className="flex items-center justify-end gap-3">
                                    <Button type="button" variant="outline" asChild>
                                        <Link href={route('paciente.agendamentos.index')}>
                                            <ArrowLeft className="mr-2 h-4 w-4" />
                                            Voltar
                                        </Link>
                                    </Button>
                                    <Button type="submit" disabled={processing || isSubmitting}>
                                        {processing || isSubmitting ? (
                                            <>
                                                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                                                Agendando...
                                            </>
                                        ) : (
                                            'Confirmar Agendamento'
                                        )}
                                    </Button>
                                </div>
                            </form>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
