import { Badge } from '@/components/ui/badge';
import PatientCard from '@/components/patient-card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import React from 'react';
import { Head, Link, router } from '@inertiajs/react';
import { AlertTriangle, Calendar, CheckCircle, Clock, CreditCard, Download, Eye, Filter, Search } from 'lucide-react';

interface Pagamento {
    id: number;
    valor: number;
    status: 'pendente' | 'pago' | 'falhou' | 'cancelado';
    forma_pagamento: string;
    data_vencimento: string;
    data_pagamento?: string;
    transaction_id?: string;
    formatted_valor: string;
    formatted_data_vencimento: string;
    formatted_data_pagamento?: string;
    assinatura: {
        id: number;
        plano: {
            id: number;
            name: string;
            price: number;
        };
    };
}

interface Stats {
    total_pago: number;
    total_pendente: number;
    total_vencido: number;
    count_pendente: number;
}

interface Props {
    pagamentos: {
        data: Pagamento[];
        links: any[];
        meta?: {
            total?: number;
            current_page?: number;
            last_page?: number;
            per_page?: number;
            from?: number;
            to?: number;
        };
    };
    filters: {
        status?: string;
        periodo?: string;
        search?: string;
    };
    stats: Stats;
    proximosVencimentos: Pagamento[];
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Início', href: '/paciente/dashboard' },
    { title: 'Pagamentos', href: '/paciente/pagamentos' },
];

export default function PagamentosIndex({ pagamentos, filters, stats, proximosVencimentos }: Props) {
    const [status, setStatus] = React.useState<string>(filters.status ?? '');
    const [periodo, setPeriodo] = React.useState<string>(filters.periodo ?? '');
    const [search, setSearch] = React.useState<string>(filters.search ?? '');

    const onSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        router.get(
            route('paciente.pagamentos.index'),
            {
                status: status || undefined,
                periodo: periodo || undefined,
                search: search || undefined,
            },
            { preserveState: true, replace: true },
        );
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            pendente: { variant: 'secondary' as const, icon: Clock, color: 'text-yellow-600' },
            pago: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
            falhou: { variant: 'destructive' as const, icon: AlertTriangle, color: 'text-red-600' },
            cancelado: { variant: 'outline' as const, icon: AlertTriangle, color: 'text-gray-600' },
        };

        const config = variants[status as keyof typeof variants] || variants.pendente;
        const Icon = config.icon;

        return (
            <Badge variant={config.variant} className="flex items-center gap-1">
                <Icon className="h-3 w-3" />
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
        );
    };

    const getFormaPagamentoBadge = (forma: string) => {
        const formas = {
            cartao_credito: 'Cartão de Crédito',
            cartao_debito: 'Cartão de Débito',
            pix: 'PIX',
            boleto: 'Boleto',
        };

        return (
            <Badge variant="outline" className="flex items-center gap-1">
                <CreditCard className="h-3 w-3" />
                {formas[forma as keyof typeof formas] || forma}
            </Badge>
        );
    };

    const isVencido = (dataVencimento: string, status: string) => {
        return status === 'pendente' && new Date(dataVencimento) < new Date();
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Meus Pagamentos" />

            <div className="mx-auto w-full max-w-7xl space-y-6 px-4 py-6 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="mb-8">
                    <h2 className="text-2xl font-bold tracking-tight sm:text-3xl">Meus Pagamentos</h2>
                    <p className="text-muted-foreground">Acompanhe seus pagamentos e faturas</p>
                </div>

                {/* Estatísticas */}
                <div className="grid grid-cols-2 gap-4 sm:gap-6 lg:gap-8 lg:grid-cols-4">
                    <PatientCard
                        icon={
                            <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                <CheckCircle className="h-5 w-5" />
                            </Badge>
                        }
                        title="Total Pago"
                        subtitle=""
                        value={`R$ ${stats.total_pago.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}
                        valueClassName="text-green-600"
                        helperText="Valor total já pago"
                    />

                    <PatientCard
                        icon={
                            <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                <Clock className="h-5 w-5" />
                            </Badge>
                        }
                        title="Pendentes"
                        subtitle=""
                        value={`R$ ${stats.total_pendente.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}
                        valueClassName="text-yellow-600"
                        helperText={`${stats.count_pendente} pagamentos pendentes`}
                    />

                    <PatientCard
                        icon={
                            <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                <AlertTriangle className="h-5 w-5" />
                            </Badge>
                        }
                        title="Vencidos"
                        subtitle=""
                        value={`R$ ${stats.total_vencido.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}
                        valueClassName="text-red-600"
                        helperText="Pagamentos em atraso"
                    />

                    <PatientCard
                        icon={
                            <Badge variant="gradient" size="icon-lg" className="h-10 w-10">
                                <Calendar className="h-5 w-5" />
                            </Badge>
                        }
                        title="Próximos"
                        subtitle=""
                        value={proximosVencimentos.length}
                        valueClassName="text-blue-600"
                        helperText="Próximos 30 dias"
                    />
                </div>

                {/* Próximos Vencimentos */}
                    {proximosVencimentos.length > 0 && (
                        <Card className="mb-6">
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Calendar className="mr-2 h-5 w-5" />
                                    Próximos Vencimentos
                                </CardTitle>
                                <CardDescription>Pagamentos que vencem nos próximos 30 dias</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    {proximosVencimentos.map((pagamento) => (
                                        <div key={pagamento.id} className="flex items-center justify-between rounded-lg border p-3">
                                            <div className="flex items-center gap-4">
                                                <div>
                                                    <p className="font-medium">{pagamento.assinatura.plano.name}</p>
                                                    <p className="text-sm text-gray-600">Vence em {pagamento.formatted_data_vencimento}</p>
                                                </div>
                                                {getStatusBadge(pagamento.status)}
                                            </div>
                                            <div className="flex items-center gap-4">
                                                <span className="text-lg font-bold">{pagamento.formatted_valor}</span>
                                                <Link href={route('paciente.pagamentos.show', pagamento.id)}>
                                                    <Button size="sm">Pagar</Button>
                                                </Link>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Busca e Filtros (igual ao Agendamentos) */}
                    <Card className="mb-6">
                        <CardContent className="p-6">
                            <form onSubmit={onSubmit} className="space-y-4">
                                <div className="flex flex-col gap-4 sm:flex-row sm:items-end">
                                    <div className="flex-1">
                                        <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
                                            Buscar Pagamentos
                                        </label>
                                        <Input
                                            id="search"
                                            type="text"
                                            placeholder="Ex.: plano, forma de pagamento..."
                                            value={search}
                                            onChange={(e) => setSearch(e.target.value)}
                                        />
                                    </div>

                                    <div className="w-full sm:w-48">
                                        <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                                            Status
                                        </label>
                                        <Select value={status || '#'} onValueChange={(v) => setStatus(v === '#' ? '' : v)}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Todos os status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="#">Todos os status</SelectItem>
                                                <SelectItem value="pendente">Pendente</SelectItem>
                                                <SelectItem value="pago">Pago</SelectItem>
                                                <SelectItem value="falhou">Falhou</SelectItem>
                                                <SelectItem value="cancelado">Cancelado</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="w-full sm:w-48">
                                        <label htmlFor="periodo" className="block text-sm font-medium text-gray-700 mb-2">
                                            Período
                                        </label>
                                        <Select value={periodo || '#'} onValueChange={(v) => setPeriodo(v === '#' ? '' : v)}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Todos os períodos" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="#">Todos os períodos</SelectItem>
                                                <SelectItem value="mes_atual">Mês atual</SelectItem>
                                                <SelectItem value="mes_anterior">Mês anterior</SelectItem>
                                                <SelectItem value="ultimos_3_meses">Últimos 3 meses</SelectItem>
                                                <SelectItem value="ano_atual">Ano atual</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <Button type="submit" className="w-full sm:w-auto">
                                        <Search className="mr-2 h-4 w-4" />
                                        Pesquisar
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>

                    

                    {/* Lista de Pagamentos */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Histórico de Pagamentos</CardTitle>
                            <CardDescription>{pagamentos.meta?.total ?? pagamentos.data.length} pagamentos encontrados</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {pagamentos.data.map((pagamento) => (
                                    <div
                                        key={pagamento.id}
                                        className={`flex items-center justify-between rounded-lg border p-4 hover:bg-gray-50 ${
                                            isVencido(pagamento.data_vencimento, pagamento.status) ? 'border-red-200 bg-red-50' : ''
                                        }`}
                                    >
                                        <div className="flex-1">
                                            <div className="mb-2 flex items-center gap-4">
                                                <div className="font-medium">{pagamento.assinatura.plano.name}</div>
                                                {getStatusBadge(pagamento.status)}
                                                {getFormaPagamentoBadge(pagamento.forma_pagamento)}
                                                {isVencido(pagamento.data_vencimento, pagamento.status) && (
                                                    <Badge variant="destructive">
                                                        <AlertTriangle className="mr-1 h-3 w-3" />
                                                        Vencido
                                                    </Badge>
                                                )}
                                            </div>

                                            <div className="flex items-center gap-6 text-sm text-gray-600">
                                                <div className="flex items-center">
                                                    <Calendar className="mr-1 h-4 w-4" />
                                                    <span>Venc: {pagamento.formatted_data_vencimento}</span>
                                                </div>
                                                {pagamento.data_pagamento && (
                                                    <div className="flex items-center">
                                                        <CheckCircle className="mr-1 h-4 w-4 text-green-600" />
                                                        <span>Pago: {pagamento.formatted_data_pagamento}</span>
                                                    </div>
                                                )}
                                                <div className="text-lg font-bold">{pagamento.formatted_valor}</div>
                                            </div>
                                        </div>

                                        <div className="flex items-center gap-2">
                                            <Link href={route('paciente.pagamentos.show', pagamento.id)}>
                                                <Button variant="outline" size="sm">
                                                    <Eye className="h-4 w-4" />
                                                </Button>
                                            </Link>
                                            {pagamento.status === 'pago' && (
                                                <Link href={route('paciente.pagamentos.comprovante', pagamento.id)}>
                                                    <Button variant="outline" size="sm">
                                                        <Download className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                            )}
                                        </div>
                                    </div>
                                ))}

                                {pagamentos.data.length === 0 && (
                                    <div className="py-8 text-center text-gray-500">Nenhum pagamento encontrado com os filtros aplicados.</div>
                                )}
                            </div>

                            {/* Paginação */}
                            {pagamentos.links && pagamentos.links.length > 3 && (
                                <div className="mt-6 flex justify-center">
                                    <div className="flex flex-wrap items-center justify-center gap-2">
                                        {pagamentos.links.map((link, index) => (
                                            <Link
                                                key={index}
                                                href={link.url || '#'}
                                                className={`inline-flex items-center rounded-md px-3 py-2 text-sm h-9 ${
                                                    link.active ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                                } ${!link.url ? 'cursor-not-allowed opacity-50' : ''}`}
                                                dangerouslySetInnerHTML={{ __html: link.label }}
                                            />
                                        ))}
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>
            </div>
        </AppLayout>
    );
}
