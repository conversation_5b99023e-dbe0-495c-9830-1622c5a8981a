import { Button } from '@/components/ui/button';
import { ToastProvider } from '@/components/ui/toast-notification';
import { TooltipProvider } from '@/components/ui/tooltip';
import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { ReactNode } from 'react';

interface PublicLayoutProps {
    children: ReactNode;
    title?: string;
    description?: string;
}

interface SocialConfig {
    facebook: {
        url: string;
        enabled: boolean;
    };
    instagram: {
        url: string;
        enabled: boolean;
    };
    whatsapp: {
        number: string;
        url: string;
        enabled: boolean;
    };
    linkedin: {
        url: string;
        enabled: boolean;
    };
    contact: {
        phone: string;
        email: string;
        address: string;
    };
}

export default function PublicLayout({ children, title, description }: PublicLayoutProps) {
    const { auth } = usePage<SharedData>().props;

    // Configurações das redes sociais
    const socialConfig: SocialConfig = {
        facebook: {
            url: 'https://www.facebook.com/share/15XRPuFkux/?mibextid=wwXIfr',
            enabled: true,
        },
        instagram: {
            url: 'https://www.instagram.com/f4.fisio?igsh=azVxbHU3cmU2aDVw&utm_source=qr',
            enabled: true,
        },
        whatsapp: {
            number: '5511978196207',
            url: 'https://wa.me/5511978196207',
            enabled: true,
        },
        linkedin: {
            url: '#',
            enabled: false,
        },
        contact: {
            phone: '(11) 97819-6207',
            email: '<EMAIL>',
            address: 'São Paulo, SP',
        },
    };

    return (
        <>
            <Head title={title}>
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700" rel="stylesheet" />
                {description && <meta name="description" content={description} />}
            </Head>

            {/* Providers */}
            <ToastProvider />
            <TooltipProvider>
                {/* Header */}
                <header className="sticky top-0 z-50 border-b bg-background shadow-sm">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex h-16 items-center justify-between">
                            <div className="flex items-center">
                                <Link href={route('home')} className="flex items-center">
                                    <img src="/images/logo.png" alt="F4 Fisio" className="h-8 w-auto" loading="eager" />
                                </Link>
                            </div>

                            {/* Mobile Menu Button */}
                            <div className="md:hidden">
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => {
                                        const mobileMenu = document.getElementById('mobile-menu');
                                        if (mobileMenu) {
                                            mobileMenu.classList.toggle('hidden');
                                        }
                                    }}
                                >
                                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                    </svg>
                                </Button>
                            </div>
                            <nav className="hidden space-x-8 md:flex">
                                <Link href={route('home')} className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary">
                                    Início
                                </Link>
                                <Link
                                    href={route('buscar')}
                                    className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
                                >
                                    Buscar Fisioterapeutas
                                </Link>
                                <Link
                                    href={route('sobre')}
                                    className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
                                >
                                    Sobre Nós
                                </Link>
                                <Link
                                    href={route('servicos')}
                                    className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
                                >
                                    Serviços
                                </Link>
                                <Link
                                    href={route('afiliados')}
                                    className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
                                >
                                    Afiliados
                                </Link>
                                <Link
                                    href={route('contato')}
                                    className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
                                >
                                    Contato
                                </Link>
                            </nav>
                            <div className="flex items-center gap-3">
                                {auth.user ? (
                                    <Button asChild>
                                        <Link href={route('dashboard')}>Dashboard</Link>
                                    </Button>
                                ) : (
                                    <>
                                        <Button variant="outline" asChild>
                                            <Link href={route('login')}>Entrar</Link>
                                        </Button>
                                        <Button asChild>
                                            <Link href={route('register')}>Cadastrar</Link>
                                        </Button>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Mobile Menu */}
                    <div id="mobile-menu" className="hidden border-t bg-background md:hidden">
                        <div className="space-y-1 px-4 pt-2 pb-3">
                            <Link
                                href={route('home')}
                                className="block rounded-md px-3 py-2 text-base font-medium text-muted-foreground hover:bg-muted hover:text-primary"
                            >
                                Início
                            </Link>
                            <Link
                                href={route('buscar')}
                                className="block rounded-md px-3 py-2 text-base font-medium text-muted-foreground hover:bg-muted hover:text-primary"
                            >
                                Buscar Fisioterapeutas
                            </Link>
                            <Link
                                href={route('sobre')}
                                className="block rounded-md px-3 py-2 text-base font-medium text-muted-foreground hover:bg-muted hover:text-primary"
                            >
                                Sobre
                            </Link>
                            <Link
                                href={route('servicos')}
                                className="block rounded-md px-3 py-2 text-base font-medium text-muted-foreground hover:bg-muted hover:text-primary"
                            >
                                Serviços
                            </Link>
                            <Link
                                href={route('afiliados')}
                                className="block rounded-md px-3 py-2 text-base font-medium text-muted-foreground hover:bg-muted hover:text-primary"
                            >
                                Afiliados
                            </Link>
                            <Link
                                href={route('contato')}
                                className="block rounded-md px-3 py-2 text-base font-medium text-muted-foreground hover:bg-muted hover:text-primary"
                            >
                                Contato
                            </Link>
                            <div className="border-t pt-4">
                                {auth.user ? (
                                    <Button asChild className="w-full justify-start">
                                        <Link href={route('dashboard')}>Dashboard</Link>
                                    </Button>
                                ) : (
                                    <div className="space-y-2">
                                        <Button variant="ghost" asChild className="w-full justify-start">
                                            <Link href={route('login')}>Entrar</Link>
                                        </Button>
                                        <Button asChild className="w-full justify-start">
                                            <Link href={route('register')}>Cadastrar</Link>
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </header>

                {/* Main Content */}
                <main className="min-h-screen">{children}</main>

                {/* Footer */}
                <footer className="bg-primary text-primary-foreground">
                    <div className="mx-auto max-w-7xl px-4 py-20 sm:px-6 lg:px-8">
                        <div className="grid gap-8 md:grid-cols-4">
                            <div>
                                <h3 className="mb-4 text-2xl font-medium">F4 Fisio</h3>
                                <p className="text-base leading-relaxed text-primary-foreground/80">
                                    Fisioterapia domiciliar de qualidade. Cuidado personalizado no conforto da sua casa.
                                </p>
                                <div className="mt-6 flex space-x-4">
                                    {/* Instagram */}
                                    {socialConfig.instagram.enabled && (
                                        <a
                                            href={socialConfig.instagram.url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-primary-foreground/80 transition-colors hover:text-primary-foreground"
                                            title="Instagram"
                                        >
                                            <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                                            </svg>
                                        </a>
                                    )}

                                    {/* Facebook */}
                                    {socialConfig.facebook.enabled && (
                                        <a
                                            href={socialConfig.facebook.url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-primary-foreground/80 transition-colors hover:text-primary-foreground"
                                            title="Facebook"
                                        >
                                            <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                                            </svg>
                                        </a>
                                    )}

                                    {/* WhatsApp */}
                                    {socialConfig.whatsapp.enabled && (
                                        <a
                                            href={socialConfig.whatsapp.url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-primary-foreground/80 transition-colors hover:text-primary-foreground"
                                            title="WhatsApp"
                                        >
                                            <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488" />
                                            </svg>
                                        </a>
                                    )}
                                </div>
                            </div>

                            <div>
                                <h4 className="mb-4 text-base font-semibold text-primary-foreground">Navegação</h4>
                                <ul className="space-y-3 text-sm text-primary-foreground/80">
                                    <li>
                                        <Link href="/" className="transition-colors hover:text-primary-foreground">
                                            Início
                                        </Link>
                                    </li>
                                    <li>
                                        <Link href="/buscar" className="transition-colors hover:text-primary-foreground">
                                            Buscar Fisioterapeutas
                                        </Link>
                                    </li>
                                    <li>
                                        <Link href="/sobre" className="transition-colors hover:text-primary-foreground">
                                            Sobre Nós
                                        </Link>
                                    </li>
                                    <li>
                                        <Link href="/servicos" className="transition-colors hover:text-primary-foreground">
                                            Serviços
                                        </Link>
                                    </li>
                                    <li>
                                        <Link href="/afiliados" className="transition-colors hover:text-primary-foreground">
                                            Afiliados
                                        </Link>
                                    </li>
                                    <li>
                                        <Link href="/contato" className="transition-colors hover:text-primary-foreground">
                                            Contato
                                        </Link>
                                    </li>
                                </ul>
                            </div>

                            <div>
                                <h4 className="mb-4 text-base font-semibold text-primary-foreground">Contato</h4>
                                <ul className="space-y-3 text-sm text-primary-foreground/80">
                                    <li>{socialConfig.contact.phone}</li>
                                    <li>{socialConfig.contact.email}</li>
                                    <li>{socialConfig.contact.address}</li>
                                </ul>
                            </div>

                            <div>
                                <h4 className="mb-4 text-base font-semibold text-primary-foreground">Links Úteis</h4>
                                <ul className="space-y-3 text-sm text-primary-foreground/80">
                                    <li>
                                        <Link href="/servicos" className="transition-colors hover:text-primary-foreground">
                                            Serviços
                                        </Link>
                                    </li>
                                    <li>
                                        <a href="/#faq" className="transition-colors hover:text-primary-foreground">
                                            FAQ
                                        </a>
                                    </li>
                                    <li>
                                        <Link href="/politica-privacidade" className="transition-colors hover:text-primary-foreground">
                                            Política de Privacidade
                                        </Link>
                                    </li>
                                    <li>
                                        <Link href="/termos-uso" className="transition-colors hover:text-primary-foreground">
                                            Termos de Uso
                                        </Link>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div className="mt-12 border-t border-primary-foreground/20 pt-8 text-center text-sm text-primary-foreground/70">
                            <p>&copy; 2025 F4 Fisio. Todos os direitos reservados.</p>
                        </div>
                    </div>
                </footer>
            </TooltipProvider>
        </>
    );
}
