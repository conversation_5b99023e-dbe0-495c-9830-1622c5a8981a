import { Head, useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ArrowLeft } from 'lucide-react';
import { Link } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';

interface Agendamento {
    id: number;
    data_agendamento: string;
    horario: string;
    status: string;
    endereco_atendimento: {
        logradouro: string;
        complemento: string;
        cidade: string;
        estado: string;
        cep: string;
        tipo: string;
    };
}

interface Props {
    agendamento: Agendamento;
}

export default function EditAddress({ agendamento }: Props) {
    const { data, setData, put, processing, errors } = useForm({
        address: agendamento.endereco_atendimento.logradouro,
        address_line2: agendamento.endereco_atendimento.complemento,
        address_city: agendamento.endereco_atendimento.cidade,
        address_state: agendamento.endereco_atendimento.estado,
        address_zip_code: agendamento.endereco_atendimento.cep,
        address_type: agendamento.endereco_atendimento.tipo,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('paciente.agendamentos.update-address', agendamento.id));
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: 'long',
            year: 'numeric',
            weekday: 'long',
        });
    };

    const formatTime = (timeString: string) => {
        return timeString.substring(0, 5);
    };

    const breadcrumbs = [
        { name: 'Agendamentos', href: route('paciente.agendamentos.index') },
        { name: 'Detalhes', href: route('paciente.agendamentos.show', agendamento.id) },
        { name: 'Editar Endereço', href: '#' },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Editar Endereço do Agendamento" />

            <div className="mx-auto w-full max-w-4xl space-y-6 px-4 py-6 sm:px-6 lg:px-8">
                <div className="mb-8">
                    <div className="mb-4">
                        <Link href={route('paciente.agendamentos.show', agendamento.id)}>
                            <Button variant="ghost" size="sm">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Voltar ao Agendamento
                            </Button>
                        </Link>
                    </div>
                    <h1 className="text-2xl font-bold tracking-tight sm:text-3xl">
                        Editar Endereço do Atendimento
                    </h1>
                    <p className="text-muted-foreground">
                        {formatDate(agendamento.data_agendamento)} às {formatTime(agendamento.horario)}
                    </p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Endereço do Atendimento</CardTitle>
                            <CardDescription>
                                Atualize as informações do local onde será realizada a sessão
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="address_type">Tipo de Endereço *</Label>
                                    <select
                                        id="address_type"
                                        value={data.address_type}
                                        onChange={(e) => setData('address_type', e.target.value)}
                                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    >
                                        <option value="residencia">Residência</option>
                                        <option value="trabalho">Trabalho</option>
                                        <option value="outro">Outro</option>
                                    </select>
                                    {errors.address_type && (
                                        <p className="text-sm text-red-600">{errors.address_type}</p>
                                    )}
                                </div>

                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="address">Logradouro *</Label>
                                        <Input
                                            id="address"
                                            placeholder="Rua, Avenida, etc."
                                            value={data.address}
                                            onChange={(e) => setData('address', e.target.value)}
                                        />
                                        {errors.address && (
                                            <p className="text-sm text-red-600">{errors.address}</p>
                                        )}
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="address_line2">Complemento</Label>
                                        <Input
                                            id="address_line2"
                                            placeholder="Apartamento, bloco, sala..."
                                            value={data.address_line2}
                                            onChange={(e) => setData('address_line2', e.target.value)}
                                        />
                                        {errors.address_line2 && (
                                            <p className="text-sm text-red-600">{errors.address_line2}</p>
                                        )}
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="address_city">Cidade *</Label>
                                        <Input
                                            id="address_city"
                                            placeholder="Nome da cidade"
                                            value={data.address_city}
                                            onChange={(e) => setData('address_city', e.target.value)}
                                        />
                                        {errors.address_city && (
                                            <p className="text-sm text-red-600">{errors.address_city}</p>
                                        )}
                                    </div>
                                    <div className="grid grid-cols-2 gap-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="address_state">Estado *</Label>
                                            <Input
                                                id="address_state"
                                                placeholder="EX"
                                                value={data.address_state}
                                                onChange={(e) => setData('address_state', e.target.value)}
                                                maxLength={2}
                                            />
                                            {errors.address_state && (
                                                <p className="text-sm text-red-600">{errors.address_state}</p>
                                            )}
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="address_zip_code">CEP *</Label>
                                            <Input
                                                id="address_zip_code"
                                                placeholder="00000-000"
                                                value={data.address_zip_code}
                                                onChange={(e) => setData('address_zip_code', e.target.value)}
                                            />
                                            {errors.address_zip_code && (
                                                <p className="text-sm text-red-600">{errors.address_zip_code}</p>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <div className="flex justify-end space-x-4">
                        <Link href={route('paciente.agendamentos.show', agendamento.id)}>
                            <Button type="button" variant="outline">
                                Cancelar
                            </Button>
                        </Link>
                        <Button type="submit" disabled={processing}>
                            {processing ? 'Salvando...' : 'Salvar Alterações'}
                        </Button>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
