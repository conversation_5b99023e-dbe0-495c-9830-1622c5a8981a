<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Alterar o enum para adicionar 'a caminho'
        DB::statement("ALTER TABLE agendamentos MODIFY COLUMN status ENUM('agendado', 'confirmado', 'em_andamento', 'concluido', 'cancelado', 'pendente', 'a caminho') DEFAULT 'agendado'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remover 'a caminho' do enum
        DB::statement("ALTER TABLE agendamentos MODIFY COLUMN status ENUM('agendado', 'confirmado', 'em_andamento', 'concluido', 'cancelado', 'pendente') DEFAULT 'agendado'");
    }
};
